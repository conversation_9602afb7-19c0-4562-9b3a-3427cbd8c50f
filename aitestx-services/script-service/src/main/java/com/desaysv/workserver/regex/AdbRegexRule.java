package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.action_sequence.RegexMatcher;

public class AdbRegexRule extends BaseRegexRule {
    private static final String CLICK = wholeMatchCaseInsensitive("Click");
    private static final String SWIPE = wholeMatchCaseInsensitive("Swipe");
    private static final String AGENT = wholeMatchCaseInsensitive("Agent");
    public static final String SEND_CONSTANT = wholeMatchCaseInsensitive(ADB_SEND);


    /**
     * 功能：ADB点击
     * 格式：ADB-Click-X-Y
     */
    public static final String ADB_CLICK = wholeCombine(CLICK, group(oneRequiredPlusZeroOrMoreSeparated(add(NUMBER, SPLITTER, NUMBER))));
    /**
     * 功能：ADB滑动
     * 格式：ADB-Swipe-X-Y
     */
    public static final String ADB_SWIPE = wholeCombine(SWIPE, group(NUMBER), group(NUMBER), group(NUMBER), group(NUMBER));


    /**
     * 功能：ADB发送
     * 格式：ADB-Send-(X)
     */
    public static final String SEND = wholeCombine(SEND_CONSTANT, group(PARENTHESIS_COMMAND));

    /**
     * 功能：调用GUIAgent模型
     * 格式：ADB-Agent-自然语言案例
     */
    public static final String GUI_AGENT_EXECUTE = wholeCombine(AGENT, group(WORD_BLANK));

    public static void main(String[] args) {
        System.out.println(ADB_CLICK);
        RegexMatcher matcher = BaseRegexRule.match("Click-1", ADB_CLICK);
        System.out.println(matcher.getMatcher().group());
    }
}
