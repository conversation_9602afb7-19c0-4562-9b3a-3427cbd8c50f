package com.desaysv.workserver.executor;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.util.TypeUtils;
import com.desaysv.workserver.action_sequence.ActionSequenceConfigManager;
import com.desaysv.workserver.action_sequence.ActionSequenceLock;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.components.DeviceActionSequenceHeader;
import com.desaysv.workserver.components.SequenceWaitTime;
import com.desaysv.workserver.devices.CommonDeviceHandler;
import com.desaysv.workserver.devices.ICommonDevice;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceExecutionException;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceStopException;
import com.desaysv.workserver.executor.procedure.IProcedure;
import com.desaysv.workserver.executor.procedure.ProcedureHandler;
import com.desaysv.workserver.finder.ActionSequenceDeviceFinderFactory;
import com.desaysv.workserver.operation.handlers.CommonOperationHandler;
import com.desaysv.workserver.operation.handlers.ICommonHandler;
import com.desaysv.workserver.result.ActionSequenceCheckResults;
import com.desaysv.workserver.result.ActionSequenceResultSet;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.ReflectUtils;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.desaysv.workserver.finder.OperationTargetFinderManager.finderDeviceTypeMap;


@Slf4j
@Service
@Lazy
public class BaseActionSequenceExecutor implements ActionSequenceExecutorStrategy {

    @Setter
    private ExecutionContext executionContext;
    @Getter
    private final Map<String, Map<String, RegexMethod>> regexMethodMap = new HashMap<>();
    @Autowired
    private ActionSequenceDeviceFinderFactory actionSequenceDeviceFinderFactory;
    @Autowired
    private CommonOperationHandler commonOperationHandler;
    @Autowired
    private CommonDeviceHandler commonDeviceHandler;
    @Autowired
    private ProcedureHandler procedureHandler;
    @Autowired
    private ApplicationContext applicationContext;
    private static final String DEFAULT_EXECUTOR_NAME = "";

    public List<Object> getBeansOfType(Class<?> targetInterface) {
        Map<String, ?> beans = applicationContext.getBeansOfType(targetInterface);
        List<Object> runningBeans = new ArrayList<>();
        for (Map.Entry<String, ?> entry : beans.entrySet()) {
            runningBeans.add(applicationContext.getBean(entry.getKey()));
        }
        return runningBeans;
    }

    private Map<String, RegexMethod> loadMethodsByClass(Class<?> executorClass) {
        Method[] methods = ReflectionUtils.getDeclaredMethods(executorClass);
        Map<String, RegexMethod> exectorMethodMap = new HashMap<>();
        //使用SPel进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        for (Method method : methods) {
            if (method.isAnnotationPresent(RegexRule.class)) {
                for (String rule : method.getAnnotation(RegexRule.class).rule()) {
                    RegexMethod regexMethod = new RegexMethod();
                    regexMethod.setMethod(method);
                    regexMethod.setClazz(executorClass);
                    exectorMethodMap.put(parser.parseExpression(rule).getValue(String.class), regexMethod);
                }

            }
        }
        return exectorMethodMap;
    }

    protected void loadMethodMap(String executorName) {
        Map<String, RegexMethod> exectorMethodMap = regexMethodMap.get(executorName);
        if (exectorMethodMap == null) {
            Class<?> executorClass = getExecutorClass();
            if (executorClass == null) {
                executorClass = finderDeviceTypeMap.get(executorName).getClazz();
            }
            if (executorClass != null) {
                regexMethodMap.put(executorName, loadMethodsByClass(executorClass));
            }
        }
    }

    protected void loadDefaultMethodMap() {
        Map<String, RegexMethod> exectorMethodMap = regexMethodMap.get(DEFAULT_EXECUTOR_NAME);
        if (exectorMethodMap == null) {
            Map<String, RegexMethod> commonMethodMap = loadMethodsByClass(ICommonHandler.class);
            Map<String, RegexMethod> procedureMethodMap = loadMethodsByClass(IProcedure.class);
            Map<String, RegexMethod> commonDeviceMethodMap = loadMethodsByClass(ICommonDevice.class);
            commonMethodMap.putAll(procedureMethodMap);
            commonMethodMap.putAll(commonDeviceMethodMap);
            regexMethodMap.put(DEFAULT_EXECUTOR_NAME, commonMethodMap);
        }
    }

    public static boolean containDeviceChannel(Method method) {
        return ReflectUtils.getMethod(method, Integer.class, Operation.DEVICE_CHANNEL_OFFICIAL_NAME);
    }

    private Map<String, RegexMethod> sort(String executorName) {
        Map<String, RegexMethod> executorMethodMap = regexMethodMap.get(executorName);
        if (executorMethodMap != null) {
            // 将Map的key按照长度从大到小排序
            Map<String, RegexMethod> sortedMap = new TreeMap<>((o1, o2) -> {
                if (o1.length() > o2.length()) {
                    return -1;
                } else if (o1.length() < o2.length()) {
                    return 1;
                } else {
                    //if equal return alphabetical order
                    return o1.compareTo(o2);
                }
            });
            sortedMap.putAll(executorMethodMap);
            return sortedMap;
        }
        return new HashMap<>();
    }

    private String loadMethod(ActionSequenceUnit actionSequenceUnit) {
        String executorName;
        if (actionSequenceUnit.getActionSequenceHeader() instanceof DeviceActionSequenceHeader) {
            //设备方法
            executorName = actionSequenceUnit.getActionSequenceHeader().getExecutorName().toLowerCase();
            loadMethodMap(executorName);
        } else {
            //其他方法
            executorName = DEFAULT_EXECUTOR_NAME;
            loadDefaultMethodMap();
        }
        return executorName;
    }


    private RegexMethodMatchResult matchRegexMethod(ActionSequenceUnit actionSequence, Map<String, RegexMethod> sortedMap) {
        RegexMethodMatchResult regexMethodMatchResult = new RegexMethodMatchResult();
        for (Map.Entry<String, RegexMethod> entry : sortedMap.entrySet()) {
            String regex = entry.getKey();
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(actionSequence.getRegexRuleApplyExpression());
            regexMethodMatchResult.setMatcher(matcher);
            int remainLength = 0; //TODO：去掉正则表达式后的匹配长度要为0
            if (matcher.find() && remainLength == 0) {
                regexMethodMatchResult.setMatched(true);
                regexMethodMatchResult.setRegexMethod(entry.getValue());
                break;
            }
        }
        return regexMethodMatchResult;
    }

    private boolean executeRegexMethod(ActionSequenceUnit actionSequence,
                                       RegexMethodMatchResult regexMethodMatchResult,
                                       ActionSequenceExecutorContext actionSequenceExecutorContext,
                                       ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                                       ActionSequenceExecutorListener actionSequenceExecutorListener,
                                       ActionSequenceResultSet actionSequenceResultSet)
            throws ActionSequenceExecutionException, ActionSequenceStopException {
        OperationTarget operationTarget = getOperationTarget(actionSequence, regexMethodMatchResult.getRegexMethod());

        //匹配方法
        Method method = regexMethodMatchResult.getRegexMethod().getMethod();
        List<Class<?>> paramTypeArray = new ArrayList<>(Arrays.asList(method.getParameterTypes()));
        cleanParams(method, paramTypeArray);
        Matcher matcher = regexMethodMatchResult.getMatcher();
//        Object[] parameters = new Object[matcher.groupCount()];
        List<String> parameterList = getParameterList(matcher);
        Object[] parameters = new Object[parameterList.size()];
//        transformParams(matcher, paramTypeArray, parameters);
        transformParams(parameterList, paramTypeArray, parameters);
        log.info("执行[{}]动作序列共{}次", actionSequence.getRawExpression(),
                actionSequence.getOptions().getRepetitions());
        return executeLoop(actionSequence,
                operationTarget,
                method,
                parameters,
                actionSequenceExecutorContext,
                actionSequenceExecutorHandler,
                actionSequenceExecutorListener,
                actionSequenceResultSet);
    }

    private static List<String> getParameterList(Matcher matcher) {
        List<String> matchList = new ArrayList<>();
        for (int i = 1; i <= matcher.groupCount(); i++) {
            String matchString = matcher.group(i);
            if (matchString == null || matchString.startsWith("."))
                continue;
            matchList.add(matchString);
        }
        return matchList;
    }

    /**
     * 创建参数包装器，用于包装方法的参数
     */
    @Data
    public static class MethodExecutionContext {
        private String projectName;
        private String userName;
        private String clientName;
        private Map<String, Object> additionalParams = new HashMap<>();

        // constructors, getters, setters

        public static MethodExecutionContext fromExecutionContext(ExecutionContext executionContext) {
            MethodExecutionContext context = new MethodExecutionContext();
            if (executionContext != null) {
                context.setProjectName(executionContext.getProjectName());
                context.setUserName(executionContext.getUserName());
                context.setClientName(executionContext.getClientName());
            }
            return context;
        }
    }

    private OperationTarget getOperationTarget(ActionSequenceUnit actionSequence, RegexMethod regexMethod) throws ActionSequenceExecutionException {
        OperationTarget operationTarget;
        if (actionSequence.getActionSequenceHeader() instanceof DeviceActionSequenceHeader) {
            //设备方法
            operationTarget = actionSequenceDeviceFinderFactory.findBySequenceDeviceName(actionSequence.getActionSequenceHeader());
        } else {
            //动态指定operationTarget
            Class<?> clazz = regexMethod.getClazz();
            if (ICommonHandler.class.isAssignableFrom(clazz)) {
                operationTarget = commonOperationHandler;
            } else if (ICommonDevice.class.isAssignableFrom(clazz)) {
                operationTarget = commonDeviceHandler;
            } else {
                operationTarget = procedureHandler;
            }
        }
        if (operationTarget != null) {
            if (executionContext != null) {
                operationTarget.getExecutionContext().set(executionContext);
            }
        } else {
            if (actionSequence.getActionSequenceHeader() instanceof DeviceActionSequenceHeader) {
                //TODO:增加消息通知到前端并弹框,提示用户未连接设备
                SseUtils.pubMsg(SseConstants.TEST_STEP_EXECUTE_RESULT_SUBSCRIBE_ID, JSON.toJSONString(actionSequence));
                String warningMessage = String.format("失败原因==>设备\"%s(#%d)\"没有连接成功",
                        actionSequence.getActionSequenceHeader().getExecutorName(), ((DeviceActionSequenceHeader) actionSequence.getActionSequenceHeader()).getDeviceOrder());
//                SseUtils.pubMsg(SseConstants.DEVICE_DISCONNECT_WARNING, warningMessage);
                throw new ActionSequenceExecutionException(warningMessage);
            } else {
                String warningMessage = String.format("失败原因==>设备\"%s\"没有连接成功", actionSequence.getActionSequenceHeader().getExecutorName());
//                SseUtils.pubMsg(SseConstants.DEVICE_DISCONNECT_WARNING, warningMessage);
                throw new ActionSequenceExecutionException(String.format("失败原因==>设备\"%s\"没有连接成功", actionSequence.getActionSequenceHeader().getExecutorName()));
            }
        }
        return operationTarget;
    }

    private void transformParams(Matcher matcher, List<Class<?>> paramTypeArray, Object[] parameters) {
        for (int i = 1; i <= parameters.length; i++) {
            Class<?> paramClazz = paramTypeArray.get(i - 1);
            String matchString = matcher.group(i);
            log.info("将{}转换到{}类型", matchString, paramClazz);
            if (List.class.isAssignableFrom(paramClazz)) {
                Pattern spliterPattern = Pattern.compile(BaseRegexRule.SPLITTER);
                String[] stringList = spliterPattern.split(matchString);
                stringList = ArrayUtil.removeBlank(stringList);
//                            System.out.println("stringList:" + Arrays.toString(stringList));
                parameters[i - 1] = Arrays.asList(stringList);
            } else {
                parameters[i - 1] = TypeUtils.cast(matchString, paramClazz);
            }
        }
    }

    private void transformParams(List<String> parametersList, List<Class<?>> paramTypeArray, Object[] parameters) {
        for (int i = 1; i <= parameters.length; i++) {
            Class<?> paramClazz = paramTypeArray.get(i - 1);
            String matchString = parametersList.get(i - 1);
            log.info("将{}转换到{}类型", matchString, paramClazz);
            if (List.class.isAssignableFrom(paramClazz)) {
                Pattern spliterPattern = Pattern.compile(BaseRegexRule.SPLITTER);
                String[] stringList = spliterPattern.split(matchString);
                stringList = ArrayUtil.removeBlank(stringList);
//                            System.out.println("stringList:" + Arrays.toString(stringList));
                parameters[i - 1] = Arrays.asList(stringList);
            } else {
                parameters[i - 1] = TypeUtils.cast(matchString, paramClazz);
            }
        }
    }

    private static void cleanParams(Method method, List<Class<?>> paramTypeArray) {
        Parameter[] parameterArray = method.getParameters();
        for (int i = 0; i < parameterArray.length; i++) {
            Parameter parameter = parameterArray[i];
            if (parameter.getName().equals(Operation.DEVICE_CHANNEL_OFFICIAL_NAME) && parameter.getType().equals(Integer.class)) {
                paramTypeArray.remove(i);
                break;
            }
        }
    }

    private boolean needsMethodExecutionContext(Method method) {
        // 检查方法是否需要MethodExecutionContext参数
        return Arrays.stream(method.getParameterTypes())
                .anyMatch(paramType -> paramType.equals(MethodExecutionContext.class));
    }

    /**
     * 单行loop  xx-xx-repeat-3
     *
     * @param actionSequence
     * @param operationTarget
     * @param method
     * @param parameters
     * @param actionSequenceExecutorContext
     * @param actionSequenceExecutorHandler
     * @param actionSequenceExecutorListener
     * @param actionSequenceResultSet
     * @return
     * @throws ActionSequenceStopException
     */
    private boolean executeLoop(ActionSequenceUnit actionSequence,
                                OperationTarget operationTarget,
                                Method method,
                                Object[] parameters,
                                ActionSequenceExecutorContext actionSequenceExecutorContext,
                                ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                                ActionSequenceExecutorListener actionSequenceExecutorListener,
                                ActionSequenceResultSet actionSequenceResultSet) throws ActionSequenceStopException {
        boolean isCyclesPass = true;
        int failedCount = 0;
        log.debug("动作序列是否暂停:{}，是否停止:{}", actionSequenceExecutorHandler.shouldPause(), actionSequenceExecutorHandler.shouldStop());
        for (int cycle = 0; cycle < actionSequence.getOptions().getRepetitions(); cycle++) {
            if (actionSequenceExecutorHandler.shouldPause()) {
//                ActionSequenceExecutorHandlerImpl handler = (ActionSequenceExecutorHandlerImpl) actionSequenceExecutorHandler;
                synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                    try {
                        log.info("动作序列执行已被暂停");
                        ActionSequenceLock.getInstance().getPauseLock().wait();
                    } catch (InterruptedException e) {
                        throw new ActionSequenceStopException("动作序列执行已被中断");
                    }
                }
            }
            if (actionSequenceExecutorHandler.shouldStop()) {
                String error = String.format("动作序列\"%s\"执行已被强制停止", actionSequence.getRawExpression());
                throw new ActionSequenceStopException(error);
            }
            log.info("执行动作序列[{}]第{}次", actionSequence.simpleExpression(), cycle + 1);
            try {
                Object actualExpectedResult;
                List<Object> parametersList = new ArrayList<>(Arrays.asList(parameters));
                if (containDeviceChannel(method) && actionSequence.isDeviceActionSequence()) {
                    //TODO:如下通过OperationCommand集中处理
//                            OperationCommand.callOperationMethod(operationTarget, actionSequence.getActionSequenceHeader().getDeviceChannel(),new Operation());
                    parametersList.add(0, ((DeviceActionSequenceHeader) actionSequence.getActionSequenceHeader()).getDeviceChannel());
                }

                if (!StrUtils.isEmpty(actionSequence.getOptions().getCheckedContext())) {
                    parametersList.add(actionSequence.getOptions().getCheckedContext());
                }
                //如果RegexRule注解的参数enableContext=true，则增加一个参数，传入当前序列的原始表达式，用于传入到CANoe端
                if (method.isAnnotationPresent(RegexRule.class) && method.getAnnotation(RegexRule.class).enableContext()) {
                    parametersList.add(actionSequence.getRawExpression());
                }

                if (needsMethodExecutionContext(method)) {
                    MethodExecutionContext methodContext = MethodExecutionContext.fromExecutionContext(executionContext);
                    parametersList.add(methodContext);
                }

                waitIfTimeSet(actionSequence.getOptions().getBeforeSequenceWaitTime(), actionSequenceExecutorContext);
                actualExpectedResult = method.invoke(operationTarget, parametersList.toArray());
                ActualExpectedResult result;
                if (actualExpectedResult instanceof ActualExpectedResult) {
                    result = (ActualExpectedResult) actualExpectedResult;
                } else if (actualExpectedResult instanceof Boolean) {
                    result = ActualExpectedResult.defaultResult((Boolean) actualExpectedResult, actualExpectedResult);
                } else {
                    result = ActualExpectedResult.defaultResult(actualExpectedResult);
                }
                //add by haiyu for upgrade~~~~~~~~~start
                if (actionSequenceExecutorListener == null) {
                    return result.isPass();
                }
                //add by haiyu for upgrade~~~~~~~~~end
//                actionSequenceExecutorListener.cycleExecuteResult(result.isPass());
                actionSequence.setExecuteOk(result.isPass());
                actionSequenceExecutorListener.cycleExecuteResult(actionSequence);
                if (actionSequenceResultSet != null) {
                    actionSequenceExecutorListener.cycleCheckResult(actionSequenceResultSet, result);
                }
                if (!result.isPass()) {
                    //结果失败
                    isCyclesPass = false;
                }
            } catch (IllegalAccessException | InvocationTargetException e) {
                isCyclesPass = false;
                ActionSequencesLoggerUtil.error("动作序列执行异常:{}", ExceptionUtils.getExceptionString(e.getCause()));
                actionSequenceExecutorListener.cycleException(e);
            } finally {
                if (!isCyclesPass){failedCount++;}
                if (isCyclesPass || ActionSequenceConfigManager.getConfig().getImageAlgorithmConfig().isContinueExecuteCurrentRow()) {// 只有isCyclesPass成功或者开启即使失败仍继续执行时才执行操作步骤
                    waitIfTimeSet(actionSequence.getOptions().getAfterSequenceWaitTime(), actionSequenceExecutorContext);
                    //每个循环都检查
                    ActionSequenceExecutorContext newActionSequenceExecutorContext =
                            afterCheck(actionSequence,
                            actionSequenceExecutorContext,
                            actionSequenceExecutorHandler,
                            actionSequenceExecutorListener
                    );
                    isCyclesPass = !newActionSequenceExecutorContext.isExecutionFailed();
                    if (!isCyclesPass){failedCount++;}
                }
            }
        }
        return failedCount == 0;
    }

    private void waitIfTimeSet(SequenceWaitTime sequenceWaitTime, ActionSequenceExecutorContext actionSequenceExecutorContext) {
        if (actionSequenceExecutorContext.isSimulated()) {
            return;
        }
        float waitTime = 0;
        boolean waitTimeRandom = sequenceWaitTime.isWaitTimeRandomEnabled();
        if (waitTimeRandom) {
            float lowerSeconds = sequenceWaitTime.getLowerTime();
            float upperSeconds = sequenceWaitTime.getUpperTime();
            if (lowerSeconds < upperSeconds) {
                waitTime = (float) RandomUtil.randomDouble(lowerSeconds, upperSeconds);
            }
        } else {
            waitTime = sequenceWaitTime.getWaitTime();
        }
        if (waitTime > 0) {
            try {
                ActionSequencesLoggerUtil.info("等待:{}s", waitTime);
                Thread.sleep((long) (waitTime * 1000L));
            } catch (InterruptedException e) {
                log.warn("中断时间等待");
            }
        }
    }

    /**
     * 检查期望结果序号标记
     *
     * @param actionSequenceUnit 待检查动作序列单元
     * @param resultCheckMarker  期望结果序号标记
     */
    private void verifyResultChecker(ActionSequenceUnit actionSequenceUnit, List<String> resultCheckMarker) {
        List<String> checkIds = actionSequenceUnit.getOptions().getCheckResultIds();
        if (!ArrayUtil.isEmpty(checkIds)) {
            for (String checkId : checkIds) {
                if (checkId != null && !resultCheckMarker.contains(checkId) && !checkId.equalsIgnoreCase(BaseRegexRule.CHECK_ALL)) {
                    actionSequenceUnit.error(String.format("%s没有包含在结果检查序号列表%s里面", checkId, resultCheckMarker));
                    actionSequenceUnit.setCheckOk(false);
                }
            }
        }
    }

    /**
     * 检查动作序列正则表达式
     *
     * @param actionSequenceUnit 待检查动作序列单元
     */
    private void verifyRegexRule(ActionSequenceUnit actionSequenceUnit) {
        if (actionSequenceUnit.isCheckOk()) {
            String executorName = loadMethod(actionSequenceUnit);
            Map<String, RegexMethod> sortedMap = sort(executorName);
            RegexMethodMatchResult regexMethodMatchResult = matchRegexMethod(actionSequenceUnit, sortedMap);
            boolean isCheckOk = regexMethodMatchResult.isMatched();
            actionSequenceUnit.setCheckOk(isCheckOk);
            if (!isCheckOk) {
                actionSequenceUnit.error(String.format("%s正则表达式匹配失败", actionSequenceUnit));
            }
        }
    }


    /**
     * 检查动作序列语法
     *
     * @param actionSequence    动作序列
     * @param resultCheckMarker 期望结果序号标记（期望结果序号，如1、2、3）
     * @return
     */
    @Override
    public boolean check(ActionSequenceUnit actionSequence, List<String> resultCheckMarker) {
        //检查结果序号
        verifyResultChecker(actionSequence, resultCheckMarker);
        //检查正则表达式
        verifyRegexRule(actionSequence);
        return actionSequence.isCheckOk();
    }

    /**
     * 根据用户序号查找动作序列单元
     *
     * @param expectedActionSequenceList 待查找动作序列列表
     * @param userSequenceOrder          用户序号
     * @return 动作序列单元
     */
    private ActionSequenceUnit findActionSequenceByUserSequenceOrder(List<ActionSequence> expectedActionSequenceList, String userSequenceOrder) {
        return (ActionSequenceUnit) expectedActionSequenceList.stream()
                .filter(actionSequence -> actionSequence.getUserSequenceOrder().equals(userSequenceOrder))
                .findFirst()
                .orElse(null);
    }

    @Override
    public boolean execute(ActionSequenceUnit actionSequence,
                           ActionSequenceExecutorContext actionSequenceExecutorContext,
                           ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                           ActionSequenceExecutorListener actionSequenceExecutorListener,
                           ActionSequenceResultSet actionSequenceResultSet) throws ActionSequenceStopException {
        if (actionSequenceExecutorHandler.shouldStop()) {
            String error = String.format("动作序列\"%s\"执行已被强制停止", actionSequence.getRawExpression());
            throw new ActionSequenceStopException(error);
        }
        long startMills = System.currentTimeMillis();
        if (actionSequenceExecutorHandler.shouldPause()) {
//            ActionSequenceExecutorHandlerImpl handler = (ActionSequenceExecutorHandlerImpl) actionSequenceExecutorHandler;
            synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                try {
                    ActionSequencesLoggerUtil.info("当前动作序列执行已被暂停");
                    ActionSequenceLock.getInstance().getPauseLock().wait();
                } catch (InterruptedException e) {
                    throw new ActionSequenceStopException("当前动作序列执行已被中断");
                }
            }
        }
        ActionSequencesLoggerUtil.info(String.format("%s：", actionSequence.getRawExpression()));
        ActionSequencesLoggerUtil.info(">>>动作序列正在执行");
        log.info(">>>动作序列上下文:{} 耗时:{}ms", actionSequenceExecutorContext, System.currentTimeMillis() - startMills);
        SseUtils.pubMsg(SseConstants.TEST_STEP_EXECUTE_SUBSCRIBE_ID, actionSequence.getTestStepUUID());

        actionSequence.setExecuted(true);
        boolean executeOk;

        if (actionSequenceExecutorContext.isSimulated()
                && actionSequence.getActionSequenceHeader().getExecutorName().equalsIgnoreCase("Wait")) {
            //模拟测试，等待时间
            executeOk = true;
        } else {
            String executorName = loadMethod(actionSequence);
            Map<String, RegexMethod> sortedMap = sort(executorName);
            RegexMethodMatchResult regexMethodMatchResult = matchRegexMethod(actionSequence, sortedMap);
            log.info("正则表达式匹配{}, 耗时:{}ms", regexMethodMatchResult.isMatched() ? "成功" : "失败", System.currentTimeMillis() - startMills);
            if (regexMethodMatchResult.isMatched()) { //FIXME:减少重新的检查
                try {
                    executeOk = executeRegexMethod(
                            actionSequence,
                            regexMethodMatchResult,
                            actionSequenceExecutorContext,
                            actionSequenceExecutorHandler,
                            actionSequenceExecutorListener,
                            actionSequenceResultSet);
                } catch (ActionSequenceExecutionException e) {
                    executeOk = false;
                    ActionSequencesLoggerUtil.warn(e.getMessage(), e);
                    actionSequence.error(e.getMessage());
                    if (actionSequenceExecutorListener != null) {
                        actionSequenceExecutorListener.cycleException(e);
                    }
                }
            } else {
                executeOk = false;
                String error = String.format("%s没有找到对应的正则表达式", actionSequence.getRegexRuleApplyExpression());
                ActionSequencesLoggerUtil.warn(error);
                actionSequence.error(error);
            }
        }
        ActionSequencesLoggerUtil.info(">>>动作序列执行{}, 耗时:{}ms", executeOk ? "成功" : "失败", System.currentTimeMillis() - startMills);
        ActionSequencesLoggerUtil.info("~~~~~~~~~~~~~~~~~~~~~~~~~~");
        actionSequence.setExecuteOk(executeOk);
        return executeOk;
    }

    private ActionSequenceExecutorContext afterCheck(ActionSequenceUnit actionSequence,
                                                     ActionSequenceExecutorContext actionSequenceExecutorContext,
                                                     ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                                                     ActionSequenceExecutorListener actionSequenceExecutorListener) throws ActionSequenceStopException {
        List<ActionSequence> expectedActionSequenceList = actionSequenceExecutorContext.getExpectedActionSequenceList();
        ActionSequenceCheckResults actionSequenceCheckResults = actionSequenceExecutorContext.getActionSequenceCheckResults();
        if (expectedActionSequenceList != null && !expectedActionSequenceList.isEmpty()) {
            List<String> checkIds = actionSequence.getOptions().getCheckResultIds();
            ActionSequencesLoggerUtil.info("检查动作序列序号:{}", checkIds);
            if (!ArrayUtil.isEmpty(checkIds)) {
                for (String checkId : checkIds) {
                    if (checkId.equalsIgnoreCase(BaseRegexRule.CHECK_ALL)) {
                        ActionSequencesLoggerUtil.info("检查全部动作序列");
                        for (ActionSequence sequence : expectedActionSequenceList) {
                            String checkResultId = sequence.getUserSequenceOrder();
                            boolean checkOk = executeSequenceChecker(
                                    expectedActionSequenceList.indexOf(sequence),
                                    checkResultId,
                                    actionSequenceExecutorContext,
                                    actionSequenceExecutorHandler,
                                    actionSequenceExecutorListener,
                                    sequence,
                                    actionSequenceCheckResults);
                            if (!checkOk) {
                                actionSequenceExecutorContext.setExecutionFailed(true);
                                log.info("Check动作序列失败: {}", actionSequence.getRawExpression());
                                if (!ActionSequenceConfigManager.getConfig().getImageAlgorithmConfig().isContinueExecuteCurrentRow()) {//失败时，不继续执行其他步骤
                                    break;// 终止执行
                                }
                            }
                        }
                    } else {
                        boolean checkOk = executeSequenceChecker(checkId,
                                actionSequenceExecutorContext,
                                actionSequenceExecutorHandler,
                                actionSequenceExecutorListener,
                                expectedActionSequenceList,
                                actionSequenceCheckResults);
                        if (!checkOk) {
                            actionSequenceExecutorContext.setExecutionFailed(true);
                            log.info("Check动作序列失败: {}", actionSequence.getRawExpression());
                            if (!ActionSequenceConfigManager.getConfig().getImageAlgorithmConfig().isContinueExecuteCurrentRow()) {//失败时，不继续执行其他步骤
                                break;// 终止执行
                            }
                        }
                    }
                }
            }
            //增加cycle checker
            List<String> cycleCheckIds = actionSequence.getOptions().getCycleCheckResultIds();
            if (!ArrayUtil.isEmpty(cycleCheckIds)) {
                if (!ArrayUtil.isEmpty(cycleCheckIds)) {
                    float cycleTime = actionSequence.getOptions().getCycleTime();
                    long timeoutMillis = (long) (cycleTime * 1000); // 转换为毫秒
                    long startTime = System.currentTimeMillis();
                    while (System.currentTimeMillis() - startTime < timeoutMillis) {
                        for (String cycleCheckId : cycleCheckIds) {
                            // 每次执行前检查是否超时
                            if (System.currentTimeMillis() - startTime >= timeoutMillis) {
                                break;
                            }
                            executeSequenceChecker(
                                    cycleCheckId,
                                    actionSequenceExecutorContext,
                                    actionSequenceExecutorHandler,
                                    actionSequenceExecutorListener,
                                    expectedActionSequenceList,
                                    actionSequenceCheckResults
                            );
                        }
                    }
                    actionSequenceExecutorHandler.setTestExecute(true);
                    actionSequenceExecutorHandler.setTestExecute(false);
                }
            }
        }
        return actionSequenceExecutorContext;
    }

    private boolean executeSequenceChecker(int checkRow,
                                           String checkResultId,
                                           ActionSequenceExecutorContext actionSequenceExecutorContext,
                                           ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                                           ActionSequenceExecutorListener actionSequenceExecutorListener,
                                           ActionSequence expectedActionSequence,
                                           ActionSequenceCheckResults actionSequenceCheckResults) throws ActionSequenceStopException {
//        if (checkResultId != null) {
        log.info("--------------------------------------");
        log.info(">>>检查期望结果编号:{}", checkResultId);
//            ActionSequenceUnit checkedResultActionSequence = findActionSequenceByUserSequenceOrder(expectedActionSequenceList, checkResultId);
        ActionSequenceUnit checkedResultActionSequence = (ActionSequenceUnit) expectedActionSequence;
        if (checkedResultActionSequence != null) {
            LinkedHashMap<Integer, String> checkMap = new LinkedHashMap<>();
            checkMap.put(checkRow, checkResultId);
            ActionSequenceResultSet actionSequenceResultSet = actionSequenceCheckResults.getResultSetByCheckId(checkMap);
//                        actionSequenceResultSet.setResultSequenceExpression(StrUtils.removeSuffix(checkedResultActionSequence.getHeaderExpression(), BaseRegexRule.SPLITTER));
            actionSequenceResultSet.setResultSequenceExpression(StrUtils.removeSuffix(checkedResultActionSequence.getActualResultExpression(), BaseRegexRule.SPLITTER));
            return execute(checkedResultActionSequence,
                    actionSequenceExecutorContext,
//                                ActionSequenceExecutorContext.build(null, actionSequenceCheckResults),
                    actionSequenceExecutorHandler,
                    actionSequenceExecutorListener,
                    actionSequenceResultSet);
        } else {
            log.warn(">>>无法找到序列结果检查编号:{}", checkResultId);
        }
//        }
        return false;
    }


    private boolean executeSequenceChecker(String checkResultId,
                                           ActionSequenceExecutorContext actionSequenceExecutorContext,
                                           ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                                           ActionSequenceExecutorListener actionSequenceExecutorListener,
                                           List<ActionSequence> expectedActionSequenceList,
                                           ActionSequenceCheckResults actionSequenceCheckResults) throws ActionSequenceStopException {
        if (checkResultId != null) {
            log.info("--------------------------------------");
            log.info(">>>检查期望结果编号:{}", checkResultId);
            ActionSequenceUnit checkedResultActionSequence = findActionSequenceByUserSequenceOrder(expectedActionSequenceList, checkResultId);
            if (checkedResultActionSequence != null) {
                LinkedHashMap<Integer, String> checkMap = new LinkedHashMap<>();
                checkMap.put(expectedActionSequenceList.indexOf(checkedResultActionSequence), checkResultId);
                ActionSequenceResultSet actionSequenceResultSet = actionSequenceCheckResults.getResultSetByCheckId(checkMap);
//                        actionSequenceResultSet.setResultSequenceExpression(StrUtils.removeSuffix(checkedResultActionSequence.getHeaderExpression(), BaseRegexRule.SPLITTER));
                actionSequenceResultSet.setResultSequenceExpression(StrUtils.removeSuffix(checkedResultActionSequence.getActualResultExpression(), BaseRegexRule.SPLITTER));
                return execute(checkedResultActionSequence,
                        actionSequenceExecutorContext,
                        actionSequenceExecutorHandler,
                        actionSequenceExecutorListener,
                        actionSequenceResultSet);
            } else {
                log.warn(">>>无法找到序列结果检查编号:{}", checkResultId);
            }
        }
        return false;
    }


    public static void main(String[] args) {
//        String regex = "^([0-9]*\\.?[0-9]+)[-——]((?!\\d+$)[\\p{Script=Han}\\w]+$)";
//        String regex = "^(?:\\b(?i)Sig(?!\\+)\\b)[-——](0[xX][0-9a-fA-F]+)[-——](\\w+)[-——]([0-9]*\\.?[0-9]+)$";
//        String regex = "^((?:\\b(?i)(?:on|off)(?!\\+)\\b))[-——]?((?!\\d+$)[\\p{Script=Han}\\w]+$)?$";
        String regex = "^((?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]+)?[-——](?:\\b(?i)Sig(?!\\+)\\b)[-——]([\\u4E00-\\u9FA5A-Za-z0-9_]+)[-——]([\\u4E00-\\u9FA5A-Za-z0-9_]+)[-——](\\(\\-\\d+(\\.\\d+)?\\)|\\d+(\\.\\d+)?)$";
        String line = "CAN-Sig-PEPS2-SysPowerMods-2";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(line);
        boolean match = matcher.find();
        if (match) {
            System.out.println(matcher.group(1));
            System.out.println(matcher.group(2));
        }
    }

}
