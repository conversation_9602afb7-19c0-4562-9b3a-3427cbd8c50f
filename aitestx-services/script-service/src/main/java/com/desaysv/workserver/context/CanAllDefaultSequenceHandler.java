package com.desaysv.workserver.context;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 处理CAN总线默认检查步骤的上下文处理器
 * 用于在测试流程中自动添加默认的CAN总线检查步骤
 */
@Service
public class CanAllDefaultSequenceHandler {
    private static final Pattern CAN_LINE_PATTERN = Pattern.compile(".*CAN.*", Pattern.CASE_INSENSITIVE);

    /**
     * 向测试流程中添加默认CAN总线检查步骤
     *
     * 处理逻辑：
     * 1. 检查全局配置是否启用默认检查
     * 2. 分析前提条件和操作步骤中是否包含CAN检查
     * 3. 提取设备ID并生成标准化的默认检查步骤
     * 4. 将默认步骤插入到步骤列表头部
     *
     * @param context 测试流程上下文对象，包含测试配置和步骤列表
     */
    public void addDefaultAllChecks(ActionSequenceContext context){
        if (!context.getTestConfig().getImageAlgorithmConfig().isEnableDefaultAll()) return;
        List<TestStep> precondition = context.getPrecondition();
        List<TestStep> operation = context.getOperationStep();
        if(precondition == null && operation == null) return;

        // 检测两个步骤列表中是否包含CAN检查项
        boolean preconditionContainsCan = containsCanCheck(precondition);
        boolean operationContainsCan = containsCanCheck(operation);
        if ((!preconditionContainsCan) && (!operationContainsCan)) return;

        // 优先从前提条件提取设备ID，否则从操作步骤提取
        Integer deviceId;
        if (preconditionContainsCan){
            deviceId = extractDeviceId(precondition);
        } else {
            deviceId = extractDeviceId(operation);
        }

        // 创建标准化的默认检查步骤模板
        TestStep firstDefaultAllStep = new TestStep();
        firstDefaultAllStep.setTestStep("CAN#" + deviceId + "-1-Default");
        TestStep secondDefaultAllStep = new TestStep();
        secondDefaultAllStep.setTestStep("CAN#" + deviceId + "-2-Default");

        // 构建新的步骤列表
        List<TestStep> newPreconditionSteps = new ArrayList<>();
        newPreconditionSteps.add(firstDefaultAllStep);
        newPreconditionSteps.add(secondDefaultAllStep);

        // 将原有步骤追加到默认步骤之后并更新上下文
        if (precondition != null) {
            newPreconditionSteps.addAll(precondition);
            context.setPrecondition(newPreconditionSteps);
        } else {
            newPreconditionSteps.addAll(operation);
            context.setOperationStep(newPreconditionSteps);
        }
    }

    /**
     * 检查步骤列表中是否存在CAN总线检查项
     *
     * @param steps 待检查的测试步骤列表
     * @return 如果存在匹配的CAN检查项返回true，否则返回false
     */
    private boolean containsCanCheck(List<TestStep> steps) {
        if (steps == null) return false;
        return steps.stream()
                .anyMatch(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        CAN_LINE_PATTERN.matcher(step.getTestStep()).find());
    }

    /**
     * 从步骤列表中提取设备ID
     *
     * 提取逻辑：
     * 1. 查找第一个包含CAN标识的步骤
     * 2. 使用正则表达式提取设备ID
     * 3. 异常情况返回默认值1
     *
     * @param steps 包含设备ID的测试步骤列表
     * @return 提取到的设备ID，解析失败返回默认值1
     */
    private Integer extractDeviceId(List<TestStep> steps) {
        return steps.stream()
                .filter(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        CAN_LINE_PATTERN.matcher(step.getTestStep()).find())
                .findFirst()
                .map(step -> {
                    Matcher matcher = Pattern.compile("CAN#(\\d+)-", Pattern.CASE_INSENSITIVE)
                            .matcher(step.getTestStep());
                    if (matcher.find()) {
                        try {
                            return Integer.parseInt(matcher.group(1));
                        } catch (NumberFormatException e) {
                            return 1;
                        }
                    }
                    return 1;
                })
                .orElse(1);
    }
}
