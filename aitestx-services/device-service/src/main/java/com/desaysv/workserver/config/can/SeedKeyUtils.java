package com.desaysv.workserver.config.can;

import com.desaysv.workserver.utils.ByteUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/7/24 10:58
 * @Version: 1.0
 * @Desc : 描述信息
 */
public class SeedKeyUtils {

    private static String path = null;

    // 注意方法名首字母大写，与DLL中一致
    public native VKeyGenResultEx GenerateKeyEx(
            byte[] ipSeedArray, int iSeedArraySize,
            int iSecurityLevel, String ipVariant,
            byte[] iopKeyArray, int iMaxKeyArraySize,
            int[] oActualKeyArraySize); // 使用int数组来模拟引用传递


    // 添加一个新的方法，直接返回生成的密钥字节数组
    public byte[] generateKey(
            byte[] ipSeedArray, int iSeedArraySize,
            int iSecurityLevel, String ipVariant) {

        // 创建足够大的缓冲区来存储生成的密钥
        byte[] iopKeyArray = new byte[16]; // 根据实际需要调整大小
        int[] actualKeyArraySize = new int[1];

        // 调用现有的包装方法
        VKeyGenResultEx result = generateKeyExWrapper(
                ipSeedArray, iSeedArraySize,
                iSecurityLevel, ipVariant,
                iopKeyArray, iopKeyArray.length,
                actualKeyArraySize
        );

        // 检查结果
        if (result.getValue() == 0) {
            // 成功，返回实际生成的密钥数据
            byte[] actualKey = new byte[actualKeyArraySize[0]];
            System.arraycopy(iopKeyArray, 0, actualKey, 0, actualKeyArraySize[0]);
            return actualKey;
        } else {
            // 失败，返回null
            System.err.println("Key generation failed with code: " + result.getValue());
            return null;
        }
    }

    // 在SeedKeyUtils类中添加包装方法
    public VKeyGenResultEx generateKeyExWrapper(
            byte[] ipSeedArray, int iSeedArraySize,
            int iSecurityLevel, String ipVariant,
            byte[] iopKeyArray, int iMaxKeyArraySize,
            int[] oActualKeyArraySize) {

        // 检查是否应该使用Python调用（32位DLL在64位系统上）
        String arch = System.getProperty("os.arch");
        boolean isSystem64Bit = "amd64".equals(arch) || "x86_64".equals(arch);

        if (isCurrentDll64Bit != null && !isCurrentDll64Bit && isSystem64Bit) {
            // 32位DLL在64位系统上，使用Python调用
            System.out.println("Calling Python script to generate key...");
            String keyHex = callPythonScript(ipSeedArray, iSecurityLevel);

            if (keyHex != null && !keyHex.isEmpty()) {
                try {
                    // 将十六进制字符串转换为字节数组
                    byte[] keyBytes = ByteUtils.hexStringToByteArray(keyHex);

                    // 复制到输出数组
                    int copyLength = Math.min(keyBytes.length, iopKeyArray.length);
                    System.arraycopy(keyBytes, 0, iopKeyArray, 0, copyLength);
                    oActualKeyArraySize[0] = copyLength;

                    System.out.println("Key generated by Python script: " + keyHex);

                    // 返回成功结果（假设0表示成功）
                    return new VKeyGenResultEx(0);
                } catch (Exception e) {
                    System.err.println("Error processing key from Python script: " + e.getMessage());
                    return new VKeyGenResultEx(-1); // 返回错误结果
                }
            } else {
                System.err.println("Failed to get key from Python script");
                return new VKeyGenResultEx(-1); // 返回错误结果
            }
        } else {
            // 直接调用native方法
            return GenerateKeyEx(
                    ipSeedArray, iSeedArraySize,
                    iSecurityLevel, ipVariant,
                    iopKeyArray, iMaxKeyArraySize,
                    oActualKeyArraySize
            );
        }
    }

    // 在SeedKeyUtils类中添加以下方法
    private static boolean is64BitDll(String dllPath) {
        try {
            // 使用PE文件头信息判断DLL架构
            java.io.RandomAccessFile file = new java.io.RandomAccessFile(dllPath, "r");

            // 检查MZ签名
            byte[] mzHeader = new byte[2];
            file.readFully(mzHeader);
            if (mzHeader[0] != 'M' || mzHeader[1] != 'Z') {
                file.close();
                throw new IllegalArgumentException("Not a valid PE file");
            }

            // 读取PE头偏移量
            file.seek(0x3C);
            int peOffset = Integer.reverseBytes(file.readInt());

            // 跳转到PE头
            file.seek(peOffset);

            // 检查PE签名
            byte[] peHeader = new byte[4];
            file.readFully(peHeader);
            if (peHeader[0] != 'P' || peHeader[1] != 'E' || peHeader[2] != 0 || peHeader[3] != 0) {
                file.close();
                throw new IllegalArgumentException("Not a valid PE file");
            }

            // 读取Machine字段（偏移量为4）
            file.seek(peOffset + 4);
            short machine = Short.reverseBytes(file.readShort());

            file.close();

            // 判断架构
            // 0x014c = Intel 386 or later processors and compatible processors
            // 0x8664 = x64
            if (machine == 0x014c) {
                return false; // 32-bit
            } else if (machine == 0x8664) {
                return true; // 64-bit
            } else {
                System.err.println("Unknown machine type: 0x" + Integer.toHexString(machine));
                return false; // 默认认为是32位
            }
        } catch (Exception e) {
            System.err.println("Error checking DLL architecture: " + e.getMessage());
            return false;
        }
    }

    // 添加一个静态变量来存储DLL架构信息
    private static Boolean isCurrentDll64Bit = null;

    public static boolean loadDll(String methodUrl) {
        try {
            File originalFile = new File(methodUrl);
            if (!originalFile.exists()) {
                System.err.println("DLL file not found: " + methodUrl);
                return false;
            }

            // 判断DLL架构
            isCurrentDll64Bit = is64BitDll(methodUrl);
            System.out.println("DLL architecture: " + (isCurrentDll64Bit ? "64-bit" : "32-bit"));

            // 获取系统架构
            String arch = System.getProperty("os.arch");
            boolean isSystem64Bit = "amd64".equals(arch) || "x86_64".equals(arch);

            System.out.println("System architecture: " + (isSystem64Bit ? "64-bit" : "32-bit"));

            if (isCurrentDll64Bit && !isSystem64Bit) {
                System.err.println("Cannot load 64-bit DLL on 32-bit system");
                return false;
            }

            if (!isCurrentDll64Bit && isSystem64Bit) {
                // 32位DLL在64位系统上，使用Python调用
                System.out.println("32-bit DLL on 64-bit system, will use Python script for key generation");
                path = methodUrl;
                return true; // 不实际加载DLL，但返回成功
            }

            // 其他情况直接加载DLL
            System.out.println("Loading DLL from: " + methodUrl);
            System.load(methodUrl);
            return true;
        } catch (UnsatisfiedLinkError e) {
            System.err.println("Failed to load DLL: " + e.getMessage());
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    // 在SeedKeyUtils类中添加以下方法
    // 在SeedKeyUtils类中改进callPythonScript方法
    private static String callPythonScript(byte[] seedBytes, int securityLevel) {
        try {
            // 使用path作为DLL路径传递给Python脚本
            String dllPath = path != null ? path : "";
            if (dllPath.isEmpty()) {
                System.err.println("DLL path is null or empty");
                return "";
            }

            // 从类路径资源中获取 uds.py 的路径
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            URL resourceUrl = classLoader.getResource("uds.py");
            if (resourceUrl == null) {
                System.err.println("uds.py not found in classpath");
                return null;
            }

            // 将 URL 转换为文件路径
            String pythonScriptPath;

            // 如果资源是一个文件（非 JAR 内部资源）
            if ("file".equals(resourceUrl.getProtocol())) {
                Path pythonScriptPathObj = Paths.get(resourceUrl.toURI());
                pythonScriptPath = pythonScriptPathObj.toString();
            } else {
                // 如果资源在 JAR 内部，需要将其复制到临时文件
                try (InputStream inputStream = resourceUrl.openStream()) {
                    File tempFile = File.createTempFile("uds", ".py");
                    tempFile.deleteOnExit(); // 确保临时文件在 JVM 退出时删除

                    Files.copy(inputStream, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    pythonScriptPath = tempFile.getAbsolutePath();
                }
            }

            System.out.println("Python script path: " + pythonScriptPath);

            // 将参数转换为命令行参数
            String seedHex = com.desaysv.workserver.utils.ByteUtils.byteArrayToHexString(seedBytes);

            // 确保variant参数不为null，如果为null则使用空字符串
            String variantValue = "Standard";

            // 尝试多个Python命令，包括完整路径
            String[] pythonCommands = {
                    // Windows常见Python安装路径
                    System.getenv("LOCALAPPDATA") + "\\Programs\\Python\\Python313-32\\python.exe",
                    System.getenv("LOCALAPPDATA") + "\\Programs\\Python\\Python312-32\\python.exe",
                    System.getenv("LOCALAPPDATA") + "\\Programs\\Python\\Python311-32\\python.exe",
                    System.getenv("LOCALAPPDATA") + "\\Programs\\Python\\Python310-32\\python.exe",
                    System.getenv("LOCALAPPDATA") + "\\Programs\\Python\\Python39-32\\python.exe",
                    System.getenv("PROGRAMFILES") + "\\Python313-32\\python.exe",
                    System.getenv("PROGRAMFILES") + "\\Python312-32\\python.exe",
                    System.getenv("PROGRAMFILES") + "\\Python311-32\\python.exe",
                    System.getenv("PROGRAMFILES") + "\\Python310-32\\python.exe",
                    System.getenv("PROGRAMFILES") + "\\Python39-32\\python.exe",
                    System.getenv("PROGRAMFILES(X86)") + "\\Python313-32\\python.exe",
                    System.getenv("PROGRAMFILES(X86)") + "\\Python312-32\\python.exe",
                    System.getenv("PROGRAMFILES(X86)") + "\\Python311-32\\python.exe",
                    System.getenv("PROGRAMFILES(X86)") + "\\Python310-32\\python.exe",
                    System.getenv("PROGRAMFILES(X86)") + "\\Python39-32\\python.exe"
            };

            ProcessBuilder processBuilder = null;

            for (String pythonCommand : pythonCommands) {
                try {
                    System.out.println("Trying Python command: " + pythonCommand);
                    processBuilder = new ProcessBuilder(
                            pythonCommand, "--version"
                    );
                    Process versionProcess = processBuilder.start();
                    int versionExitCode = versionProcess.waitFor();
                    if (versionExitCode == 0) {
                        System.out.println("Found working Python command: " + pythonCommand);
                        // 使用找到的命令调用实际脚本，并传入DLL路径
                        processBuilder = new ProcessBuilder(
                                pythonCommand, pythonScriptPath,
                                "--seed", seedHex,
                                "--security-level", String.valueOf(securityLevel),
                                "--variant", variantValue,
                                "--dll-path", dllPath
                        );
                        break;
                    }
                } catch (Exception e) {
                    System.out.println("Python command " + pythonCommand + " not available");
                    continue;
                }
            }

            if (processBuilder == null) {
                System.err.println("无法找到可用的Python解释器，请确保Python已正确安装并添加到系统PATH中");
                return null;
            }

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            BufferedReader reader = new BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream())
            );

            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            // 读取错误输出
            StringBuilder errorOutput = new StringBuilder();
            BufferedReader errorReader = new BufferedReader(
                    new InputStreamReader(process.getErrorStream())
            );

            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                return output.toString().trim();
            } else {
                System.err.println("Python script exited with code: " + exitCode);
                System.err.println("Error output: " + errorOutput.toString());
                return null;
            }
        } catch (Exception e) {
            System.err.println("Error calling Python script: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }


    public static void main(String[] args) {
        try {
            System.out.println("Starting SeedKeyUtils test...");
            // 加载DLL
            String dllPath = "D:\\UID01728\\Downloads\\LEEA3.0_RACP(1).dll";
            if (!SeedKeyUtils.loadDll(dllPath)) {
                System.err.println("DLL loading failed");
                return;
            }

            System.out.println("DLL loaded successfully");

            SeedKeyUtils seedKeyUtils = new SeedKeyUtils();
            byte[] seedBytes = new byte[]{(byte) 0x0D , 0x01, (byte) 0x75, (byte) 0xBD, (byte) 0xB8, (byte) 0x5A, (byte) 0xC4, 0x0C, 0x14, (byte) 0x69, 0x0A, 0x07, (byte) 0xF8, (byte) 0xE9, (byte) 0x57, 0x20};

            System.out.println("Input seed: " + com.desaysv.workserver.utils.ByteUtils.byteArrayToHexString(seedBytes));

            // 使用新方法直接获取密钥
            byte[] generatedKey = seedKeyUtils.generateKey(
                    seedBytes,
                    seedBytes.length,
                    9,
                    ""
            );

            if (generatedKey != null) {
                System.out.println("Key generation successful");
                System.out.println("Generated key: " + com.desaysv.workserver.utils.ByteUtils.byteArrayToHexString(generatedKey));
            } else {
                System.err.println("Key generation failed");
            }

        } catch (UnsatisfiedLinkError e) {
            System.err.println("=== Native Link Error ===");
            System.err.println("Error message: " + e.getMessage());
            System.err.println();
            System.err.println("Troubleshooting steps:");
            System.err.println("1. Verify the DLL contains GenerateKeyEx method with exact signature");
            System.err.println("2. Check if method name case matches (GenerateKeyEx vs generateKeyEx)");
            System.err.println("3. Confirm DLL architecture matches JVM (32/64 bit)");
            System.err.println("4. Verify all DLL dependencies are available");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
