
package com.desaysv.workserver.devices.bus.e2e.other;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * E2E其他表管理器类
 * 该类用于管理E2E通信中的映射表关系，采用单例模式设计
 * 使用静态内部类方式实现线程安全的懒加载单例模式
 */
public class E2eOtherTableManager {

    /**
     * 静态内部类，用于持有单例实例
     * JVM在加载内部类时是线程安全的，保证了实例创建的线程安全性
     */
    private static class SingletonHolder {
        private static final E2eOtherTableManager INSTANCE = new E2eOtherTableManager();
    }

    /**
     * 获取E2E其他表管理器的单例实例
     * @return E2eOtherTableManager的单例实例
     */
    public static E2eOtherTableManager getInstance() {
        return SingletonHolder.INSTANCE;
    }


    // E2E映射表，使用线程安全的ConcurrentHashMap实现
    public Map<Integer, Integer> e2eMap = new ConcurrentHashMap<>();


    /**
     * 私有构造函数
     * 初始化E2E映射表，添加默认的映射关系
     * 清空现有映射表并添加"0x391"到其对应十进制值的映射
     */
    public E2eOtherTableManager(){
        e2eMap.clear();
        e2eMap.put(Integer.parseInt("0x391".substring(2), 16), Integer.parseInt("0x391".substring(2), 16));
    }
}