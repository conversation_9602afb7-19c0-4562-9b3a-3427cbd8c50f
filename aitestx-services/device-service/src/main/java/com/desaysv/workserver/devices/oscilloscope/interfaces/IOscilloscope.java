package com.desaysv.workserver.devices.oscilloscope.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.parseFrequency;

public interface IOscilloscope {
    Logger log = LogManager.getLogger(IOscilloscope.class.getSimpleName());

    Map<String, Float> getMeasureAll(Integer deviceChannel);

    float getFrequency(Integer deviceChannel);

    float getAmplitude(Integer deviceChannel);

    float getDuty(Integer deviceChannel);

    boolean setInitValue(float frequencyBaseValue);

    boolean setOscStatus(String deviceStatus);

    float getRiseTime(Integer deviceChannel);

    float getFallTime(Integer deviceChannel);

    float getTopValue(Integer deviceChannel);

    float getBaseValue(Integer deviceChannel);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).SET_MEASURE_INIT_VALUE"})
    default ActualExpectedResult setMeasureInitInfo(Integer deviceChannel, String frequencyBaseValueWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float frequencyBaseValue = parseFrequency(frequencyBaseValueWithUnit);
        boolean pass = setInitValue(frequencyBaseValue);
        actualExpectedResult.put("setMeasureInitInfo", pass, frequencyBaseValueWithUnit);
        log.info("示波器设置初始化时基、垂直分辨率事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).SET_STATUS"})
    default ActualExpectedResult setStatusInfo(Integer deviceChannel, String deviceStatus) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setOscStatus(deviceStatus);
        actualExpectedResult.put("setStatusInfo", pass, deviceStatus);
        log.info("示波器设置设备状态事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_MEASURE_ALL"})
    default ActualExpectedResult getMeasureAllInfo(Integer deviceChannel, String frequencyBaseValueWithUnit, String frequencyRangeWithUnit, float dutyValue, float dutyRange) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float frequencyBaseValue = parseFrequency(frequencyBaseValueWithUnit);
        Map<String, Float> map = getMeasureAll(deviceChannel);
        float frequency = map.get("freq");
        float dutyCycle = map.get("duty");
        float frequencyRange = parseFrequency(frequencyRangeWithUnit);
        boolean frequencyPass = frequencyBaseValue - frequencyRange <= frequency && frequency <= frequencyBaseValue + frequencyRange;
        boolean dutyPass = dutyValue - dutyRange < dutyCycle && dutyCycle < dutyValue + dutyRange;
        boolean pass = frequencyPass && dutyPass;
        actualExpectedResult.put("getMeasureAllInfo", pass, frequency + "-" + dutyCycle);
        log.info("示波器获取频率、占空比事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_FREQUENCY"})
    default ActualExpectedResult getFrequencyInfo(Integer deviceChannel, float frequencyValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float frequency = getFrequency(deviceChannel);
        boolean pass = frequency == frequencyValue;
        actualExpectedResult.put("getFrequencyInfo", pass, frequency);
        log.info("示波器获取频率事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_AMPLITUDE"})
    default ActualExpectedResult getAmplitudeInfo(Integer deviceChannel, float amplitudeValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float amplitude = getAmplitude(deviceChannel);
        boolean pass = amplitude == amplitudeValue;
        actualExpectedResult.put("getAmplitudeInfo", pass, amplitude);
        log.info("示波器获取幅值事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_DUTY"})
    default ActualExpectedResult getDutyCycleInfo(Integer deviceChannel, float dutyCycleValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float dutyCycle = getDuty(deviceChannel);
        boolean pass = dutyCycle == dutyCycleValue;
        actualExpectedResult.put("getDutyCycleInfo", pass, dutyCycle);
        log.info("示波器获取占空比事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_SINGLE_DUTY"})
    default ActualExpectedResult getSingleDutyInfo(Integer deviceChannel, float dutyValue, float dutyRange) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float duty = getDuty(deviceChannel);    //如deviceChannel=1,就对应示波器高级测量的position<1>
        boolean pass = dutyValue - dutyRange < duty && duty < dutyValue + dutyRange;
        actualExpectedResult.put("getSingleDutyInfo", pass, String.format("%s%%", duty));
        log.info("示波器获取占空比事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_TWO_DUTY"})
    default ActualExpectedResult getTwoDutyInfo(Integer deviceChannel, float dutyValueCh1, float dutyRangeCh1, int positionCh2, float dutyValueCh2, float dutyRangeCh2) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float getDutyCH1 = getDuty(deviceChannel);    //如deviceChannel=1,就对应示波器高级测量的position<1>
        float getDutyCH2 = getDuty(positionCh2);
        boolean pass = dutyValueCh1 - dutyRangeCh1 < getDutyCH1 && getDutyCH1 < dutyValueCh1 + dutyRangeCh1
                && dutyValueCh2 - dutyRangeCh2 < getDutyCH2 && getDutyCH2 < dutyValueCh2 + dutyRangeCh2;
        actualExpectedResult.put("getMultiDutyInfo", pass, String.format("%s%%-%s%%", getDutyCH1, getDutyCH2));
        log.info("示波器获取占空比事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_MULTI_DUTY"})
    default ActualExpectedResult getMultiDutyInfo(Integer deviceChannel, float dutyValueCh1, float dutyRangeCh1,
                                                  int positionCh2, float dutyValueCh2, float dutyRangeCh2,
                                                  int positionCh3, float dutyValueCh3, float dutyRangeCh3) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float getDutyCH1 = getDuty(deviceChannel);    //如deviceChannel=1,就对应示波器高级测量的position<1>
        float getDutyCH2 = getDuty(positionCh2);
        float getDutyCH3 = getDuty(positionCh3);
        boolean pass = dutyValueCh1 - dutyRangeCh1 < getDutyCH1 && getDutyCH1 < dutyValueCh1 + dutyRangeCh1
                && dutyValueCh2 - dutyRangeCh2 < getDutyCH2 && getDutyCH2 < dutyValueCh2 + dutyRangeCh2
                && dutyValueCh3 - dutyRangeCh3 < getDutyCH3 && getDutyCH3 < dutyValueCh3 + dutyRangeCh3;
        actualExpectedResult.put("getMultiDutyInfo", pass, String.format("%s%%-%s%%-%s%%", getDutyCH1, getDutyCH2, getDutyCH3));
        log.info("示波器获取占空比事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_RISE_TIME"})
    default ActualExpectedResult getRiseTimeInfo(Integer deviceChannel, float lowerValue, float upperValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float riseTime = getRiseTime(deviceChannel);    //如deviceChannel=1,就对应示波器高级测量的position<1>
        boolean pass = upperValue - lowerValue < riseTime && riseTime < lowerValue + upperValue;
        actualExpectedResult.put("getRiseTimeInfo", pass, String.format("%s", riseTime));
        log.info("示波器获取上升时间事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_FALL_TIME"})
    default ActualExpectedResult getFallTimeInfo(Integer deviceChannel, float lowerValue, float upperValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float fallTime = getFallTime(deviceChannel);    //如deviceChannel=1,就对应示波器高级测量的position<1>
        boolean pass = upperValue - lowerValue < fallTime && fallTime < lowerValue + upperValue;
        actualExpectedResult.put("getFallTimeInfo", pass, String.format("%s", fallTime));
        log.info("示波器获取下降时间事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_TOP_VALUE"})
    default ActualExpectedResult getTopValueInfo(Integer deviceChannel, float lowerValue, float upperValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float topValue = getTopValue(deviceChannel);    //如deviceChannel=1,就对应示波器高级测量的position<1>
        boolean pass = upperValue - lowerValue < topValue && topValue < lowerValue + upperValue;
        actualExpectedResult.put("getFullTimeInfo", pass, String.format("%s", topValue));
        log.info("示波器获取顶端值事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.OscilloscopeRegexRule).GET_BASE_VALUE"})
    default ActualExpectedResult getBottomValueInfo(Integer deviceChannel, float lowerValue, float upperValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float bottomValue = getBaseValue(deviceChannel);    //如deviceChannel=1,就对应示波器高级测量的position<1>
        boolean pass = upperValue - lowerValue < bottomValue && bottomValue < lowerValue + upperValue;
        actualExpectedResult.put("getBottomValueInfo", pass, String.format("%s", bottomValue));
        log.info("示波器获取底端值事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

}
