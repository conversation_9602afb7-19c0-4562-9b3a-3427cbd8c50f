package com.desaysv.workserver.config.can;

import lombok.Getter;

/**
 * 对应DLL中的VKeyGenResultEx枚举
 */
@Getter
public class VKeyGenResultEx {
    public static final int KGRE_Ok = 0;
    public static final int KGRE_BufferToSmall = 1;
    public static final int KGRE_SecurityLevelInvalid = 2;
    public static final int KGRE_VariantInvalid = 3;
    public static final int KGRE_UnspecifiedError = 4;
    public static final int KGRE_Error = -1;

    private int value;

    public VKeyGenResultEx(int value) {
        this.value = value;
    }

    @Override
    public String toString() {
        switch (value) {
            case KGRE_Ok: return "KGRE_Ok";
            case KGRE_BufferToSmall: return "KGRE_BufferToSmall";
            case KGRE_SecurityLevelInvalid: return "KGRE_SecurityLevelInvalid";
            case KGRE_VariantInvalid: return "KGRE_VariantInvalid";
            case KGRE_UnspecifiedError: return "KGRE_UnspecifiedError";
            case KGRE_Error: return "KGRE_Error";
            default: return "Unknown(" + value + ")";
        }
    }
}
