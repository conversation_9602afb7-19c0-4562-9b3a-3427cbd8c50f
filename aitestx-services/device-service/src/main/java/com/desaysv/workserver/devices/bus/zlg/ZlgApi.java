package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.can.NetCanConfigParameter;
import com.desaysv.workserver.config.lin.LinConfigParameter;
import com.desaysv.workserver.config.lin.NetLinConfigParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.exceptions.can.ZlgCanExecuteException;
import com.sun.jna.Pointer;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.desaysv.workserver.devices.bus.zlg.ZlgCanConstants.SEND_ECHO;

@Setter
@Getter
@Slf4j
public class ZlgApi {
    public static final int INVALID_DEVICE_HANDLE = 0;
    public static final int INVALID_CHANNEL_HANDLE = 0;
    public static final int ZCAN_STATUS_ERR = 0;
    public static final int ZCAN_STATUS_OK = 1;
    public static final int ZCAN_STATUS_ONLINE = 2;
    public static final int ZCAN_STATUS_OFFLINE = 3;
    public static final int ZCAN_STATUS_UNSUPPORTED = 4;
    public static final int ZCAN_CHANNEL_INDEX = 0;
    public static final int RETRY_TIMES = 5;
    public static final int RETRY_INTERVAL = 10;


    private volatile boolean readFlag = true;
    public static volatile boolean running = true;
    public static final byte ZCAN_DT_ZCAN_LIN_DATA = 4;

    private static short autoMsg = 0;

    @Getter
    private static Map<Integer,Map<Integer,Object>> map = new HashMap<>();


    public static Pointer openDevice(int deviceType, int deviceIndex) {
        return ZlgCanLib.Instance.ZCAN_OpenDevice(deviceType, deviceIndex);
    }

    public static boolean closeDevice(Pointer deviceHandle) {
        return ZlgCanLib.Instance.ZCAN_CloseDevice(deviceHandle) == ZlgCanConstants.STATUS_OK;
    }

    public static ZlgCanLib.IProperty getIProperty(Pointer deviceHandle) {
        return ZlgCanLib.Instance.GetIProperty(deviceHandle);
    }

    public static ZlgCanLib.ZCAN_DEVICE_INFO getDeviceInfo(Pointer deviceHandle) {
        ZlgCanLib.ZCAN_DEVICE_INFO info = new ZlgCanLib.ZCAN_DEVICE_INFO();
        int result = ZlgCanLib.Instance.ZCAN_GetDeviceInf(deviceHandle, info);
        if (result == ZlgCanConstants.STATUS_OK) {
            return info;
        }
        return null;
    }

    public static boolean isDeviceOnline(Pointer deviceHandle) {
        return ZlgCanLib.Instance.ZCAN_IsDeviceOnLine(deviceHandle) == ZCAN_STATUS_ONLINE;
    }

    public static ZlgCanLib.ZCAN_CHANNEL_STATUS getChannelInfo(Pointer channelHandle) {
        ZlgCanLib.ZCAN_CHANNEL_STATUS status = new ZlgCanLib.ZCAN_CHANNEL_STATUS();
        int result = ZlgCanLib.Instance.ZCAN_ReadChannelStatus(channelHandle, status);
        if (result == ZlgCanConstants.STATUS_OK) {
            return status;
        }
        return null;
    }

    /**
     * 构造报文ID
     *
     * @param id  报文ID
     * @param eff 0:标准帧 1:拓展帧
     * @param rtr 0:数据帧 1:远程帧
     * @param err 0:正常帧 1:错误帧
     * @return 报文ID
     */
    private static int makeCanId(int id, int eff, int rtr, int err) {
        return id | ((eff == 0 ? 0 : 1) << 31) | ((rtr == 0 ? 0 : 1) << 30) | ((err == 0 ? 0 : 1) << 29);
    }

    public static void sendCanPeriodMessage(Pointer deviceHandle, @NotNull CanMessage canMessage) {
        boolean isCanFd = canMessage.isCanFd();
        int canId = makeCanId(canMessage.getArbitrationId(), canMessage.isExtendedId() ? 1 : 0, canMessage.isRemoteFrame() ? 1 : 0, canMessage.isErrorFrame() ? 1 : 0);
        int len = canMessage.length();
        if (isCanFd) {
            Map<Integer, Object> channelMap = map.getOrDefault(canMessage.getChannel(), new HashMap<>());
            ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ auto_canfd;
            if (channelMap.containsKey(canMessage.getArbitrationId()) && channelMap.get(canMessage.getArbitrationId()) instanceof ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) {
                // 如果已存在相同ID，获取已有的配置对象
                auto_canfd = (ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) channelMap.get(canMessage.getArbitrationId());
                auto_canfd.enable = 1; // 重新启用
            } else {
                // 如果不存在，创建新对象并初始化索引
                auto_canfd = new ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ();
                if(channelMap.containsKey(canMessage.getArbitrationId())){
                    ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ auto = (ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) channelMap.get(canMessage.getArbitrationId());
                    auto_canfd.index = auto.index;
                }else{
                    auto_canfd.index = autoMsg;
                    autoMsg++;
                }
                auto_canfd.enable = 1;
                map.computeIfAbsent(canMessage.getChannel(), k -> new HashMap<>()).put(canMessage.getArbitrationId(), auto_canfd);
            }
            auto_canfd.interval = (int) (canMessage.getPeriod() * 1000);
            ZlgCanLib.ZCAN_TransmitFD_Data transmitFDData;
            if (auto_canfd.obj != null && auto_canfd.obj instanceof ZlgCanLib.ZCAN_TransmitFD_Data) {
                // 复用已存在的transmitFDData对象
                transmitFDData = (ZlgCanLib.ZCAN_TransmitFD_Data) auto_canfd.obj;
            } else {
                transmitFDData = new ZlgCanLib.ZCAN_TransmitFD_Data();
            }
            transmitFDData.transmitType = 0; // 正常发送
            transmitFDData.frame.canId = canId;
            transmitFDData.frame.len = (byte) len;
            transmitFDData.frame.flags = SEND_ECHO; // 发送回显
            transmitFDData.frame.data = canMessage.getData().clone();
            auto_canfd.obj = transmitFDData;
            auto_canfd.write();
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, canMessage.getChannel()-1+"/auto_send_canfd", auto_canfd.getPointer());
        } else {
            Map<Integer, Object> channelMap = map.getOrDefault(canMessage.getChannel(), new HashMap<>());
            ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ auto_can;
            if (channelMap.containsKey(canMessage.getArbitrationId()) && channelMap.get(canMessage.getArbitrationId()) instanceof ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) {
                // 如果已存在相同ID，获取已有的配置对象
                auto_can = (ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) channelMap.get(canMessage.getArbitrationId());
                auto_can.enable = 1; // 重新启用
            } else {
                // 如果不存在，创建新对象并初始化索引
                auto_can = new ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ();
                if(channelMap.containsKey(canMessage.getArbitrationId())){
                    ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ auto = (ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ)channelMap.get(canMessage.getArbitrationId());
                    auto_can.index = auto.index;
                }else{
                    auto_can.index = autoMsg;
                    autoMsg++;
                }
                auto_can.enable = 1;
                map.computeIfAbsent(canMessage.getChannel(), k -> new HashMap<>()).put(canMessage.getArbitrationId(), auto_can);
            }
            auto_can.interval = (int) (canMessage.getPeriod() * 1000);
            ZlgCanLib.ZCAN_Transmit_Data transmitData;
            if (auto_can.obj != null && auto_can.obj instanceof ZlgCanLib.ZCAN_Transmit_Data) {
                // 复用已存在的transmitData对象
                transmitData = (ZlgCanLib.ZCAN_Transmit_Data) auto_can.obj;
            } else {
                transmitData = new ZlgCanLib.ZCAN_Transmit_Data();
            }
            transmitData.transmitType = 0; // 正常发送
            transmitData.frame.canId = canId;
            transmitData.frame.canDlc = (byte) len;
            transmitData.frame.__pad = SEND_ECHO;// 发送回显
            transmitData.frame.data = canMessage.getData().clone();
            auto_can.obj = transmitData;
            auto_can.write();
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, canMessage.getChannel()-1+"/auto_send", auto_can.getPointer());
        }
        ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle,canMessage.getChannel()-1+"/apply_auto_send","0");
    }

    public static void stopCanPeriodMessage(Pointer deviceHandle,Integer channel, Integer id) {
        Map<Integer, Map<Integer, Object>> map = ZlgApi.map;
        Map<Integer, Object> idMap = map.get(channel);
        Object autoCan = idMap.get(id);
        if (autoCan instanceof ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) {
            ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) autoCan;
            obj.enable = 0; // 关闭使能
            obj.write();
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle,channel-1+"/auto_send", obj.getPointer());
        }else if(autoCan instanceof ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ){
            ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) autoCan;
            obj.enable = 0; // 关闭使能
            obj.write();
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle,channel-1+"/auto_send_canfd", obj.getPointer());
        }
    }


    public static boolean sendCanMessage(Pointer channelHandle, @NotNull CanMessage canMessage) {
        // 通道0定时100ms发送ID为0x100 CAN报文，通道1定时200ms发送ID为0x200 CANFD报文
        boolean isCanFd = canMessage.isCanFd();
        int numberSend;
        int canId = makeCanId(canMessage.getArbitrationId(), canMessage.isExtendedId() ? 1 : 0, canMessage.isRemoteFrame() ? 1 : 0, canMessage.isErrorFrame() ? 1 : 0);
        int len = canMessage.length();
        float period = canMessage.getPeriod();
        if (isCanFd) {
            ZlgCanLib.ZCAN_TransmitFD_Data transmitFDData = new ZlgCanLib.ZCAN_TransmitFD_Data();
            transmitFDData.transmitType = 0; // 正常发送
            transmitFDData.frame.canId = canId;
            transmitFDData.frame.len = (byte) len;
            transmitFDData.frame.flags = SEND_ECHO;//发送回显
            transmitFDData.frame.data = canMessage.getData().clone();
            numberSend = ZlgCanLib.Instance.ZCAN_TransmitFD(channelHandle, transmitFDData, 1);
        } else {
            ZlgCanLib.ZCAN_Transmit_Data transmitData = new ZlgCanLib.ZCAN_Transmit_Data();
            transmitData.transmitType = 0; // 正常发送
            transmitData.frame.canId = canId;
            transmitData.frame.canDlc = (byte) len;
            transmitData.frame.__pad = SEND_ECHO;//发送回显
            transmitData.frame.data = canMessage.getData().clone();
            numberSend = ZlgCanLib.Instance.ZCAN_Transmit(channelHandle, transmitData, 1);
        }
        return numberSend > 0;
    }

    /**
     * 启动CAN
     *
     * @param deviceHandle       设备句柄
     * @param canConfigParameter 通道参数
     */
    public static Pointer startCan(Pointer deviceHandle, CanConfigParameter canConfigParameter) throws ZlgCanExecuteException {
        // 获取 IProperty 指针，用于配置参数
        int channel = canConfigParameter.getChannel() - 1;
        boolean isNormalMode = canConfigParameter.isNormalWorkMode();
        boolean isCanFd = canConfigParameter.isCanFd();
        boolean isEnableResistance = canConfigParameter.isTerminalResistanceEnabled();
        boolean isStandardISO = canConfigParameter.isCanFdStandard();
        int arbitrationBps = canConfigParameter.getArbitrationBpsValue();
        int dataBps = canConfigParameter.getDataBpsValue();
        int displayChannel = canConfigParameter.getChannel();
        log.info("\n通道:{}\n波特率:{}Kbps\n数据波特率:{}Mbps\n是否CANFD:{}\n是否标准CAN:{}\n是否启用电阻:{}\n",
                displayChannel, arbitrationBps / 1000, dataBps / 1000.0 / 1000.0, isCanFd, isStandardISO, isEnableResistance);

        ZlgCanLib.IProperty property = getIProperty(deviceHandle);
        if (property == null) {
            String msg = String.format("通道%d 获取属性失败", displayChannel);
            log.error(msg);
            throw new ZlgCanExecuteException(msg);
        }
        ZlgCanPropertyManager propertyManager = new ZlgCanPropertyManager(property);
        boolean isOk;

        //设置CANFD属性
        //根据与zlg沟通 can协议时canfd_abit_baud_rate 和canfd_dbit_baud_rate都需要设置。
        //if (isCanFd) {
        //设置CANFD ISO
        isOk = propertyManager.setCanfdStandard(channel, isStandardISO ? 0 : 1);
        if (!isOk) {
            log.warn("通道{} setCanfdStandard failed", displayChannel);
        }
        //设置控制器仲裁域波特率
        isOk = propertyManager.setCanfdAbitBaudRate(channel, arbitrationBps);
        if (!isOk) {
            log.warn("通道{} setCanfdAbitBaudRate failed", displayChannel);
        }
        //设置控制器数据域波特率
        isOk = propertyManager.setCanfdDbitBaudRate(channel, dataBps);
        if (!isOk) {
            log.warn("通道{} setCanfdDbitBaudRate failed", displayChannel);
        }
        // TODO：设置通道自定义波特率
        //}
        ZlgCanLib.ZCAN_CHANNEL_INIT_CONFIG initConfig = new ZlgCanLib.ZCAN_CHANNEL_INIT_CONFIG();
        //根据与zlg沟通 启动can盒子接口的时候 只能用canfd 至于那协议的can是内部添加开关实现的 所以协议如果选can会启动不了can盒子
        initConfig.canType = canConfigParameter.isCanFd() ? 1 : 0;//isCanFd ? 1 : 0; // 0 - CAN，1 - CANFD

        if (isCanFd) {
            initConfig.canFd.mode = (byte) (isNormalMode ? 0 : 1); // 0表示正常模式（相当于正常节点）
//            initConfig.config.canFd.abitTiming = 104286;
//            initConfig.config.canFd.dbitTiming = 8487694;
        } else {
            initConfig.can.mode = (byte) (isNormalMode ? 0 : 1); // 0 - 正常模式，1 - 只听模式
        }
        Pointer channelHandle = ZlgCanLib.Instance.ZCAN_InitCAN(deviceHandle, channel, initConfig);
        if (channelHandle == null) {
            String msg = String.format("通道%d ZCAN_InitCAN失败", displayChannel);
            log.error(msg);
            throw new ZlgCanExecuteException(msg);
        }
        //清除滤波
        isOk = propertyManager.clearFilter(channel);
        if (!isOk) {
            log.warn("Channel {} clearFilter failed", displayChannel);
        }

        //标准帧滤波
        isOk = propertyManager.setFilterMode(channel, 0);
        if (!isOk) {
            log.warn("Channel {} setFilterMode failed", displayChannel);
        }
        isOk = propertyManager.setFilterStart(channel, "0");
        if (!isOk) {
            log.warn("Channel {} setFilterStart failed", displayChannel);
        }
        isOk = propertyManager.setFilterEnd(channel, "0x7FF");
        if (!isOk) {
            log.warn("Channel {} setFilterEnd failed", displayChannel);
        }

        //拓展帧滤波
        isOk = propertyManager.setFilterMode(channel, 1);
        if (!isOk) {
            log.warn("Channel {} setFilterMode failed", displayChannel);
        }
        isOk = propertyManager.setFilterStart(channel, "0");
        if (!isOk) {
            log.warn("Channel {} setFilterStart failed", displayChannel);
        }
        isOk = propertyManager.setFilterEnd(channel, "0x1FFFFFFF");
        if (!isOk) {
            log.warn("Channel {} setFilterEnd failed", displayChannel);
        }
        // 滤波生效（全部滤波ID段同时生效）
        isOk = propertyManager.setFilterAck(channel);
        if (!isOk) {
            log.warn("Channel {} setFilterAck failed", displayChannel);
        }

        //设置内部电阻使能与否
        isOk = propertyManager.setInternalResistance(channel, isEnableResistance ? 1 : 0);
        if (!isOk) {
            log.warn("通道{}设置内部电阻失败", displayChannel);
        }

        int result = ZlgCanLib.Instance.ZCAN_StartCAN(channelHandle);
        if (result == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("启动CAN通道%d失败", displayChannel));
        }

        return channelHandle;
    }


    /**
     * 启动CAN
     *
     * @param deviceHandle          设备句柄
     * @param netCanConfigParameter 通道参数
     */
    public static Pointer startCan(Pointer deviceHandle, NetCanConfigParameter netCanConfigParameter) throws ZlgCanExecuteException {
        // 获取 IProperty 指针，用于配置参数
        int channel = netCanConfigParameter.getChannel() - 1;
        int displayChannel = netCanConfigParameter.getChannel();
        boolean isCanFd = netCanConfigParameter.isCanFd();
        ZlgCanLib.IProperty property = getIProperty(deviceHandle);
        if (property == null) {
            String msg = String.format("通道%d 获取属性失败", displayChannel);
            log.error(msg);
            throw new ZlgCanExecuteException(msg);
        }
        ZlgCanPropertyManager propertyManager = new ZlgCanPropertyManager(property);
        boolean isOk;

        //根据与zlg沟通can属性都在工具配置不需要代码设置。
        ZlgCanLib.ZCAN_CHANNEL_INIT_CONFIG initConfig = new ZlgCanLib.ZCAN_CHANNEL_INIT_CONFIG();
        //根据与zlg沟通 启动can盒子接口的时候 只能用canfd 至于那协议的can是内部添加开关实现的 所以协议如果选can会启动不了can盒子
        initConfig.canType = netCanConfigParameter.isCanFd() ? 1 : 0;//isCanFd ? 1 : 0; // 0 - CAN，1 - CANFD
        Pointer channelHandle = ZlgCanLib.Instance.ZCAN_InitCAN(deviceHandle, channel, initConfig);
        if (channelHandle == null) {
            String msg = String.format("通道%d ZCAN_InitCAN失败", displayChannel);
            log.error(msg);
            throw new ZlgCanExecuteException(msg);
        }
        int result = ZlgCanLib.Instance.ZCAN_StartCAN(channelHandle);
        if (result == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("启动CAN通道%d失败", displayChannel));
        }
        return channelHandle;
    }

    /**
     * 设备不换，更改通道配置时使用这个接口
     * reset完，需要重新startCan
     *
     * @param channelHandle
     * @return
     */
    public static boolean resetCan(Pointer channelHandle, int channel) throws ZlgCanExecuteException {
        if (channelHandle == null) {
            return true;
        }
        log.info("重置通道{}", channel);
        int resetResult = ZlgCanLib.Instance.ZCAN_ResetCAN(channelHandle);
        if (resetResult == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("重置CAN通道%d失败", channel));
        }
        return true;
    }

    public static boolean stopChannel(Pointer channelHandle, int channel) throws ZlgCanExecuteException {
        if (channelHandle == null) {
            return true;
        }
        log.info("停止通道{}", channel);
        int resetResult = ZlgCanLib.Instance.ZCAN_ResetCAN(channelHandle);
        if (resetResult == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("重置CAN通道%d失败", channel));
        }
        return true;
    }

    public ZlgCanReturnData readDataByIdHex(Pointer channelHandle, int deviceChannel, int targetCanId, long timeoutMilliseconds, boolean isCanFd) {
        long startTime = System.currentTimeMillis();
        ZlgCanReturnData zlgCanReturnData;
        while (System.currentTimeMillis() - startTime <= timeoutMilliseconds) {
            int canFDNum = ZlgCanLib.Instance.ZCAN_GetReceiveNum(channelHandle, (byte) (isCanFd ? 1 : 0));
            if (canFDNum > 0) {
                if (isCanFd) {
                    // CANFD
                    zlgCanReturnData = getCanFdReturnData(channelHandle, targetCanId, canFDNum);
                } else {
                    // CAN
                    zlgCanReturnData = getCanReturnData(channelHandle, targetCanId, canFDNum);
                }
                if (zlgCanReturnData != null) {
                    return zlgCanReturnData;
                }
            }
            try {
                Thread.sleep(5L);
            } catch (InterruptedException var15) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        return null;
    }

    /**
     * 获取指定CAN ID的返回数据
     *
     * @param channelHandle  CAN通道句柄，用于标识特定的CAN通道
     * @param targetCanId    目标CAN ID，用于筛选接收到的CAN消息
     * @param canFDNum       接收缓冲区大小，用于初始化接收消息数组
     * @return ZlgCanReturnData 包含目标CAN ID的消息数据，若未找到则返回null
     */
    private ZlgCanReturnData getCanReturnData(Pointer channelHandle, int targetCanId, int canFDNum) {
        ZlgCanLib.ZCAN_Receive_Data[] canMessages = (ZlgCanLib.ZCAN_Receive_Data[]) new ZlgCanLib.ZCAN_Receive_Data().toArray(canFDNum);
        int receiveNum = ZlgCanLib.Instance.ZCAN_Receive(channelHandle, canMessages, canMessages.length);
        if (receiveNum > 0) {
            for (ZlgCanLib.ZCAN_Receive_Data data : canMessages) {
                if (data != null && data.frame != null && data.frame.canId == targetCanId) {
                    String dir;
                    if (data.frame.__pad == SEND_ECHO) {
                        dir = "Tx";
                    } else {
                        dir = "Rx";
                    }
                    byte[] actualData = new byte[data.frame.canDlc];
                    System.arraycopy(data.frame.data, 0, actualData, 0, data.frame.canDlc);
                    return new ZlgCanReturnData(data.timestamp,
                            data.frame.canId,
                            "CAN",
                            dir,
                            data.frame.canDlc,
                            actualData);
                }
            }
        }
        return null;
    }


    /**
     * 获取指定CAN ID的CAN FD返回数据
     *
     * @param channelHandle  CAN通道句柄指针，用于标识特定的CAN通道
     * @param targetCanId    目标CAN ID，用于筛选接收到的CAN消息
     * @param canFDNum       预期接收的CAN FD消息数量
     * @return ZlgCanReturnData 包含CAN FD消息数据的对象，若未找到匹配消息则返回null
     *                          - 包含时间戳、CAN ID、协议类型("CAN")、方向(Tx/Rx)、数据长度和实际数据
     */
    private ZlgCanReturnData getCanFdReturnData(Pointer channelHandle, int targetCanId, int canFDNum) {
        ZlgCanLib.ZCAN_ReceiveFD_Data[] canFDMessages = (ZlgCanLib.ZCAN_ReceiveFD_Data[]) new ZlgCanLib.ZCAN_ReceiveFD_Data().toArray(canFDNum);
        int receiveNum = ZlgCanLib.Instance.ZCAN_ReceiveFD(channelHandle, canFDMessages, canFDMessages.length);
        if (receiveNum > 0) {
            for (ZlgCanLib.ZCAN_ReceiveFD_Data data : canFDMessages) {
                if (data != null && data.frame != null && data.frame.canId == targetCanId) {
                    String dir;
                    if (data.frame.flags == SEND_ECHO) {
                        dir = "Tx";
                    } else {
                        dir = "Rx";
                    }
                    byte[] actualData = new byte[data.frame.len];
                    System.arraycopy(data.frame.data, 0, actualData, 0, data.frame.len);
                    return new ZlgCanReturnData(data.timestamp,
                            data.frame.canId,
                            "CAN",
                            dir,
                            data.frame.len,
                            actualData);
                }
            }
        }
        return null;
    }

    public boolean read(Pointer channelHandle, List<ZlgCanDataListener> canDataListeners) {
        readFlag = true;
        new Thread(() -> {
            while (readFlag && !Thread.currentThread().isInterrupted()) {
                List<ZlgCanReturnData> dataList = new ArrayList<>();

                int canNum = ZlgCanLib.Instance.ZCAN_GetReceiveNum(channelHandle, (byte) ZlgCanLib.ZCAN_CHANNEL_TYPE.CAN.getValue());
                if (canNum > 0) {
                    ZlgCanLib.ZCAN_Receive_Data[] canMessages = (ZlgCanLib.ZCAN_Receive_Data[]) new ZlgCanLib.ZCAN_Receive_Data().toArray(100);
                    int receiveNum = ZlgCanLib.Instance.ZCAN_Receive(channelHandle, canMessages, 100);
                    for (int i = 0; i < receiveNum; i++) {
                        ZlgCanLib.ZCAN_Receive_Data data = canMessages[i];
                        byte[] actualData = new byte[data.frame.canDlc];
                        String dir = data.frame.__pad == SEND_ECHO ? "Tx" : "Rx";
                        int id = data.frame.canId < 0 && data.frame.canId > 0x80000000 ?
                                data.frame.canId - 0x80000000 : data.frame.canId;
                        System.arraycopy(data.frame.data, 0, actualData, 0, data.frame.canDlc);
                        ZlgCanReturnData zlgData = new ZlgCanReturnData(data.timestamp, id, "CAN", dir, data.frame.canDlc, actualData);
                        dataList.add(zlgData);
                    }
                }

                int canFDNum = ZlgCanLib.Instance.ZCAN_GetReceiveNum(channelHandle, (byte) ZlgCanLib.ZCAN_CHANNEL_TYPE.CAN_FD.getValue());
                if (canFDNum > 0) {
                    ZlgCanLib.ZCAN_ReceiveFD_Data[] canFDMessages = (ZlgCanLib.ZCAN_ReceiveFD_Data[]) new ZlgCanLib.ZCAN_ReceiveFD_Data().toArray(100);
                    int receiveNum = ZlgCanLib.Instance.ZCAN_ReceiveFD(channelHandle, canFDMessages, 100);
                    for (int i = 0; i < receiveNum; i++) {
                        ZlgCanLib.ZCAN_ReceiveFD_Data data = canFDMessages[i];
                        byte[] actualData = new byte[data.frame.len];
                        String dir = data.frame.flags == SEND_ECHO ? "Tx" : "Rx";
                        int id = data.frame.canId < 0 && data.frame.canId > 0x80000000 ?
                                data.frame.canId - 0x80000000 : data.frame.canId;
                        System.arraycopy(data.frame.data, 0, actualData, 0, data.frame.len);
                        ZlgCanReturnData zlgData = new ZlgCanReturnData(data.timestamp, id, "CANFD", dir, data.frame.len, actualData);
                        dataList.add(zlgData);
                    }
                }
                if (!dataList.isEmpty()) {
                    // 广播给所有监听器
                    for (ZlgCanDataListener listener : canDataListeners) {
                        listener.onDataReceived(dataList);
                    }
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }).start();
        return true;
    }

    public boolean stopRead(Pointer channelHandle, int channel) throws ZlgCanExecuteException {
        readFlag = false;
        //暂停把缓冲区清除
        int resetResult = ZlgCanLib.Instance.ZCAN_ClearBuffer(channelHandle);
        if (resetResult == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("关闭CAN通道清除缓冲区%d失败", channel));
        }
        return true;
    }

    /**
     * -----------------------LIN----------------------------
     */

    public static Pointer startLin(Pointer deviceHandle, LinConfigParameter linConfigParameter) throws ZlgCanExecuteException {
        int channel = linConfigParameter.getChannel() - 1;
        boolean isEnableResistance = linConfigParameter.isTerminalResistanceEnabled();
        int linBaud = linConfigParameter.getLinBaud();
        int linMode = linConfigParameter.getLinMode();
        int displayChannel = linConfigParameter.getChannel();
        int chkSumMode = linConfigParameter.getChkSumMode();
        log.info("\n通道:{}\n波特率:{}Kbps\n主站从站：{}\n是否启用内部电阻:{}\n校验模式{}\n",
                displayChannel, linBaud, linMode, isEnableResistance, chkSumMode);

        ZlgCanLib.IProperty property = getIProperty(deviceHandle);
        if (property == null) {
            String msg = String.format("通道%d 获取属性失败", displayChannel);
            log.error(msg);
            throw new ZlgCanExecuteException(msg);
        }
        ZlgCanPropertyManager propertyManager = new ZlgCanPropertyManager(property);
        boolean isOk;

        ZlgCanLib.ZCAN_LIN_INIT_CONFIG initConfig = new ZlgCanLib.ZCAN_LIN_INIT_CONFIG((byte) linMode, (byte) chkSumMode, linBaud);
        Pointer channelHandle = ZlgCanLib.Instance.ZCAN_InitLIN(deviceHandle, channel, initConfig);

        ZlgCanLib.ZCAN_DEVICE_INFO deviceInfo = ZlgApi.getDeviceInfo(deviceHandle);
        // v1.03版本以上才支持内部电阻 768为1.03版本
        if (deviceInfo != null && !(768 == deviceInfo.hwVersion)) {
            // 设置内部电阻使能与否
            isOk = propertyManager.setInternalResistance(channel, isEnableResistance ? 1 : 0);
            if (!isOk) {
                log.warn("通道{}设置内部电阻失败", displayChannel);
            }
        } else {
            log.info("通道{}未设置内部电阻", displayChannel);
        }

        int result = ZlgCanLib.Instance.ZCAN_StartLIN(channelHandle);
        if (result == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("启动Lin通道%d失败", displayChannel));
        }
        return channelHandle;
    }

    public static Pointer startLin(Pointer deviceHandle, NetLinConfigParameter newLinConfigParameter) throws ZlgCanExecuteException {
        int channel = newLinConfigParameter.getChannel() - 1;
        boolean isEnableResistance = newLinConfigParameter.isTerminalResistanceEnabled();
        int linBaud = newLinConfigParameter.getLinBaud();
        int linMode = newLinConfigParameter.getLinMode();
        int displayChannel = newLinConfigParameter.getChannel();
        int chkSumMode = newLinConfigParameter.getChkSumMode();
        log.info("\n通道:{}\n波特率:{}Kbps\n主站从站：{}\n是否启用内部电阻:{}\n校验模式{}\n",
                displayChannel, linBaud, linMode, isEnableResistance, chkSumMode);

        ZlgCanLib.IProperty property = getIProperty(deviceHandle);
        if (property == null) {
            String msg = String.format("通道%d 获取属性失败", displayChannel);
            log.error(msg);
            throw new ZlgCanExecuteException(msg);
        }
        ZlgCanPropertyManager propertyManager = new ZlgCanPropertyManager(property);
//        boolean isOk;
//
//        //设置内部电阻使能与否
//        isOk = propertyManager.setInternalResistance(channel, isEnableResistance ? 1 : 0);
//        if (!isOk) {
//            log.warn("通道{}设置内部电阻失败", displayChannel);
//        }
        ZlgCanLib.ZCAN_LIN_INIT_CONFIG initConfig = new ZlgCanLib.ZCAN_LIN_INIT_CONFIG((byte) linMode, (byte) chkSumMode, linBaud);
        Pointer channelHandle = ZlgCanLib.Instance.ZCAN_InitLIN(deviceHandle, channel, initConfig);
        int result = ZlgCanLib.Instance.ZCAN_StartLIN(channelHandle);
        if (result == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("启动Lin通道%d失败", displayChannel));
        }
        return channelHandle;
    }

    public static boolean stopLinChannel(Pointer channelHandle, int channel) throws ZlgCanExecuteException {
        if (channelHandle == null) {
            return true;
        }
        log.info("停止LIN通道:{}", channel);
        int resetResult = ZlgCanLib.Instance.ZCAN_ResetLIN(channelHandle);
        if (resetResult == ZlgCanConstants.STATUS_ERR) {
            throw new ZlgCanExecuteException(String.format("重置LIN通道%d失败", channel));
        }
        return true;
    }


    public String readLinMessage(Pointer channelHandle, int msgId, long timeoutMilliseconds) {
        ZlgCanLib.ZCAN_LIN_MSG zlgLinReturnData = null;
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime <= timeoutMilliseconds) {
            int linNum = ZlgCanLib.Instance.ZCAN_GetLINReceiveNum(channelHandle);
            if (linNum > 0) {
                ZlgCanLib.ZCAN_LIN_MSG linMessages = new ZlgCanLib.ZCAN_LIN_MSG(1);
                linMessages.data = new ZlgCanLib.ZCAN_LIN_MSG.Data(1);
                linMessages.data.zcanlinData = new ZlgCanLib.ZCAN_LIN_MSG.ZCANLINData(1);
                long rcount = ZlgCanLib.Instance.ZCAN_ReceiveLIN(channelHandle, linMessages, 1, 100);
                if (rcount > 0) {
                    byte pid = linMessages.data.zcanlinData.PID;
                    String pidHex = Long.toHexString(byteToUnsignedLong(pid)).toUpperCase();
                    int id = extractIdFromPID(pid);
                    if (msgId == id) {
                        zlgLinReturnData = linMessages;
                        log.info("成功接收到LIN ID为:{} 的数据", msgId);
                        break;
                    }
                    System.out.println("接收到LIN消息：PID:" + pidHex + " ID: " + id + " 数据: " + Arrays.toString(linMessages.data.zcanlinData.RxData.data));
                }
            }
            try {
                Thread.sleep(5L);
            } catch (InterruptedException var15) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        return zlgLinReturnData == null ? "" : Arrays.toString(zlgLinReturnData.data.zcanlinData.RxData.data);
    }

    public long byteToUnsignedLong(byte data) {
        // 将byte值与0xFF进行按位与操作，以将其视为无符号值
        return ((long) data) & 0xFF;
    }

    // 提取PID中的ID
    private int extractIdFromPID(byte pid) {
        // PID的低6位为ID，使用位运算获取
        return pid & 0x3F;  // 0x3F = 00111111，低6位是ID
    }

    public void setReceiveMerge(Pointer channelHandle, boolean subChannel) {
        if (subChannel) {
            ZlgCanLib.Instance.ZCAN_SetValue(channelHandle, "0/set_device_recv_merge", "0");
        } else {
            ZlgCanLib.Instance.ZCAN_SetValue(channelHandle, "0/set_device_recv_merge", "1");
        }
    }

    public Pointer startLinChannel(Pointer deviceHandle, int channel, ZlgCanLib.ZCAN_LIN_INIT_CONFIG linInitConfig) {
        Pointer channelHandle = ZlgCanLib.Instance.ZCAN_InitLIN(deviceHandle, channel, linInitConfig);
        ZlgCanLib.Instance.ZCAN_StartLIN(channelHandle);
        return channelHandle;
    }

}
