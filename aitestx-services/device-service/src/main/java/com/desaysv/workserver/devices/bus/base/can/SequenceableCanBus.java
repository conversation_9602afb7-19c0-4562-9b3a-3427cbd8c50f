package com.desaysv.workserver.devices.bus.base.can;

import cantools.dbc.DbcReader;
import cantools.dbc.DecodedSignal;
import cantools.dbc.Message;
import cantools.dbc.Node;
import cantools.exceptions.DecodingFrameLengthException;
import cantools.exceptions.MessageNotFoundException;
import com.desaycv.tosuncan.exceptions.TSCanException;
import com.desaysv.workserver.action_sequence.ActionSequenceLock;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.config.can.*;
import com.desaysv.workserver.devices.bus.DbcUtils;
import com.desaysv.workserver.devices.bus.base.*;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.devices.bus.interfaces.ICanSequenceMore;
import com.desaysv.workserver.entity.DeviceContextInfo;
import com.desaysv.workserver.exceptions.SimulatedDeviceNotification;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.DataUtils;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.HEX_NUMBER;

/**
 * 支持动作序列的CAN总线
 */

public abstract class SequenceableCanBus extends CanBus implements ICanSequenceMore {
    Logger log = LogManager.getLogger(SequenceableCanBus.class.getSimpleName());

    private static final int READ_TIMEOUT = 5000;
    protected final static int TIMEOUT_MILLISECONDS = 5000;
    private final CanRecvQueueManager canRecvQueueManager;
    protected String projectName;

    public SequenceableCanBus() {
        this(new DeviceOperationParameter());
    }

    public SequenceableCanBus(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        canRecvQueueManager = new CanRecvQueueManager();
        this.projectName = deviceOperationParameter.getProject();
    }

    @Override
    public String getDeviceModel() {
        return null;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return false;
    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {

    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }

    @Override
    public void send(FlexrayMessage message, Float timeout) throws BusError {

    }

    /**
     * 写入配置文件
     *
     * @param canConfigParameter
     * @return
     */
    protected CanConfig writeConfig(CanConfigParameter canConfigParameter) {
        // 写入配置文件
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        Map<String, CanConfigParameter> configParameters = deviceConfig.getConfigParameters();

        int channel = canConfigParameter.getChannel();

        if (channel == -1) {
            for (int i = 1; i <= getMaxChannelCount(); i++) {
                CanConfigParameter clonedParam = SerializationUtils.clone(canConfigParameter);
                clonedParam.setChannel(i);
                configParameters.put(String.valueOf(i), clonedParam);
            }
        } else if (channel > 0) {
            // 单个通道
            configParameters.put(String.valueOf(channel), SerializationUtils.clone(canConfigParameter));
        }

        updateConfig(deviceConfig);
        return deviceConfig;
    }

    /**
     * 写入配置文件
     *
     * @param netCanConfigParameter
     * @return
     */
    protected CanConfig writeConfig(NetCanConfigParameter netCanConfigParameter) {
        // 写入配置文件
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        Map<String, NetCanConfigParameter> configParameters = deviceConfig.getConfigNetParameters();

        int channel = netCanConfigParameter.getChannel();

        if (channel == -1) {
            for (int i = 1; i <= 4; i++) {
                NetCanConfigParameter clonedParam = SerializationUtils.clone(netCanConfigParameter);
                clonedParam.setChannel(i);
                configParameters.put(String.valueOf(i), clonedParam);
            }
        } else if (channel > 0) {
            // 单个通道
            configParameters.put(String.valueOf(channel), SerializationUtils.clone(netCanConfigParameter));
        }

        updateConfig(deviceConfig);
        return deviceConfig;
    }

    @Override
    public int getCanMessageCycle(Integer deviceChannel, String messageName) {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            for (DbcReader reader : dbcReaders) {
                message = reader.getBus().getMessageMap().get(messageName);
                if (message != null) {
                    break;
                }
            }
            if (message != null) {
                return message.getInterval();
            }
        }
        throw new IllegalArgumentException("未找到报文" + messageName);
    }

    @Override
    public boolean sendCanData(Integer deviceChannel, String messageId, byte[] byteData, Integer period, Integer cycle, boolean isCanFd) throws BusError {
        CanMessage canMessage = new CanMessage();
        if (period == 0) {
            log.info("检测到CAN报文{}周期为0，改为发送事件帧报文", messageId);
            canMessage.setSendTimes(1);
        } else {
            canMessage.setSendTimes(cycle == null ? -1 : cycle);
        }
        canMessage.setCanFd(isCanFd);
        canMessage.setChannel(deviceChannel);
        canMessage.setArbitrationId(DataUtils.parseHexString(messageId));
        canMessage.setData(byteData);
        canMessage.setPeriod(period / 1000.0f); //s
        canMessage.setDlc(canMessage.getData().length);
        canMessage.setDuration(canMessage.getSendTimes() * canMessage.getPeriod());
        sendCanMessage(deviceChannel, canMessage);
        return true;
    }

    @Override
    public boolean stopSendPeriodicCanData(Integer deviceChannel, String messageId) {
        log.info("停止发送CAN通道{}报文{}", deviceChannel, messageId);
        // 判断messageId是否符合十六进制格式，符合的话就是报文id
        if (messageId != null && messageId.matches(HEX_NUMBER)) {
            stopCanMessage(deviceChannel, DataUtils.parseHexString(messageId));
        } else {
            //否则messageId为报文名称
            CanConfig deviceConfig = getDeviceConfig();
            DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
            try {
                if (dbcConfig != null) {
                    Message message = null;
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                    for (DbcReader dbcReader : dbcReaders) {
                        message = dbcReader.getBus().getMessageMap().get(messageId);
                        if (message != null) {
                            break;
                        }
                    }

                    if (message == null) {
                        throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageId));
                    }
                    stopCanMessage(deviceChannel, DataUtils.parseHexString(message.getId()));
                } else {
                    throw new BusError("dbc文件未找到");
                }
            } catch (Exception e) {
                log.error("停止发送CAN通道{}报文{}失败", deviceChannel, messageId);
            }
        }
        return true;
    }

    @Override
    public boolean sendAllPeriodicCanMessage(Integer deviceChannel) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            if (dbcConfig != null) {
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                for (DbcReader dbcReader : dbcReaders) {
                    List<Message> messages = dbcReader.getBus().getMessages();
                    for (Message message : messages) {
                        byte[] messageData = dbcReader.getMessageData(message.getName());
                        sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), null, message.isFd());
                    }
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (BusError e) {
            throw e;
        } catch (Exception e) {
            throw new BusError(e);
        }
        return true;
    }

    @Override
    public boolean setCanChannelMessageStatus(Integer deviceChannel, int messageStatus) throws BusError {
        if (messageStatus == 0) {
            log.info("暂停CAN通道{}所有报文", deviceChannel);
            pauseAllCanMessage(deviceChannel);
        } else {
            CanConfig deviceConfig = getDeviceConfig();
            DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
            try {
                if (dbcConfig != null) {
                    log.info("发送CAN通道{}所有报文", deviceChannel);
                    List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                    for (DbcReader dbcReader : dbcReaders) {
                        List<Message> messages = dbcReader.getBus().getMessages();
                        for (Message message : messages) {
                            setCanSingleMsgStatus(deviceChannel, null, message.getName(), messageStatus);
                        }
                    }
                } else {
                    throw new BusError("dbc文件未找到");
                }
            } catch (BusError e) {
                throw e;
            } catch (Exception e) {
                throw new BusError(e);
            }
        }
        return true;
    }

    @Override
    public String verifyCanMessage(Integer deviceChannel, String messageId, String byteData,Integer count) throws BusError {
        return "";
    }

    @Override
    public boolean setCanChannelMessageStatus(Integer deviceChannel, List<String> excludeEcus, int messageStatus) throws BusError {
        boolean ok = true;
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            Set<String> excludeEcuSet = excludeEcus.stream().map(String::toUpperCase).collect(Collectors.toSet());
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            for (DbcReader dbcReader : dbcReaders) {
                List<Node> nodes = dbcReader.getNetwork().getNode();
                for (Node node : nodes) {
                    String ecu = node.getName().toUpperCase();
                    if (excludeEcuSet.contains(ecu)) {
                        continue;
                    }
                    ok &= setCanEcuAllMsgStatus(deviceChannel, ecu, messageStatus);
                }
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return ok;
    }


    @Override
    public boolean setCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, long signalRawValue) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            if (dbcConfig != null) {
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                Message message = null;
                DbcReader dbcReader = null;
                for (int i = 0; i < dbcReaders.size(); i++) {
                    message = dbcReaders.get(i).getBus().getMessageMap().get(messageName);
                    if (message != null) {
                        dbcReader = dbcReaders.get(i);
                        break;
                    }
                }
                if (message == null) {
                    throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageName));
                }
                log.info("CAN通道{}报文{}信号{}原始值改为:0x{}", deviceChannel, messageName, signalName, String.format("%X", signalRawValue));
                CyclicTask cyclicTask = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel,
                        DataUtils.parseHexString(message.getId())));
                if (cyclicTask == null) {
                    //信号没有运行，启动报文
                    byte[] bytesBefore = dbcReader.getMessageData(messageName);
                    byte[] bytesAfter = decodeSignalRawValue(message, signalName, BigInteger.valueOf(signalRawValue), bytesBefore);
                    DeviceContextInfo.getInstance().getMessageDataMap().put(messageName,ByteUtils.byteArrayToHexString(bytesAfter));
                    sendCanData(deviceChannel, message.getId(), bytesAfter, message.getInterval(), null, message.isFd());
                } else if (cyclicTask instanceof ThreadBasedCyclicSendTask) {
                    //信号已经在运行
                    ThreadBasedCyclicSendTask task = (ThreadBasedCyclicSendTask) cyclicTask;
                    byte[] bytesBefore = task.getCanMessage().getData();
                    byte[] bytesAfter = decodeSignalRawValue(message, signalName, BigInteger.valueOf(signalRawValue), bytesBefore);
                    DeviceContextInfo.getInstance().getMessageDataMap().put(messageName,ByteUtils.byteArrayToHexString(bytesAfter));
                    task.modifyData(bytesAfter);
                    task.resume();
                } else {
                    throw new BusError("CAN报文任务类型错误");
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (BusError e) {
            throw e;
        } catch (Exception e) {
            throw new BusError(e);
        }
        return true;
    }

    /**
     * 设置指定CAN信号的物理值
     *
     * @param deviceChannel  CAN通道号(1/2)
     * @param ecuNodeName    ECU节点名称（当前参数未实际使用，保留供后续扩展）
     * @param messageName    报文名称（对应DBC文件中定义的报文名称）
     * @param signalName     信号名称（对应DBC文件中定义的信号名称）
     * @param signalPhyValue 要设置的物理值（经过缩放和偏移处理后的实际物理量值）
     * @return 总是返回true表示操作成功
     * @throws BusError 可能抛出以下异常：
     *                  1. DBC文件未找到
     *                  2. 指定报文/信号不存在
     *                  3. 报文任务类型错误
     * @implNote 实现逻辑：
     * 1. 根据通道号获取DBC配置，加载对应DBC文件
     * 2. 查找目标报文和信号定义
     * 3. 判断信号对应的周期发送任务是否存在：
     * - 若任务不存在：创建新的周期发送任务，使用修改后的信号值启动发送
     * - 若任务存在：修改运行中的报文数据，并恢复任务执行
     * 4. 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
     */
    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue) throws BusError {
        return setCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName, signalPhyValue, null);
    }

    /**
     * 设置指定CAN信号的物理值
     *
     * @param deviceChannel  CAN通道号(1/2)
     * @param ecuNodeName    ECU节点名称（当前参数未实际使用，保留供后续扩展）
     * @param messageName    报文名称（对应DBC文件中定义的报文名称）
     * @param signalName     信号名称（对应DBC文件中定义的信号名称）
     * @param signalPhyValue 要设置的物理值（经过缩放和偏移处理后的实际物理量值）
     * @param cycle          发送次数
     * @return 总是返回true表示操作成功
     * @throws BusError 可能抛出以下异常：
     *                  1. DBC文件未找到
     *                  2. 指定报文/信号不存在
     *                  3. 报文任务类型错误
     * @implNote 实现逻辑：
     * 1. 根据通道号获取DBC配置，加载对应DBC文件
     * 2. 查找目标报文和信号定义
     * 3. 判断信号对应的周期发送任务是否存在：
     * - 若任务不存在：创建新的周期发送任务，使用修改后的信号值启动发送
     * - 若任务存在：修改运行中的报文数据，并恢复任务执行
     * 4. 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
     */
    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue, Integer cycle) throws BusError {
        // 获取设备配置和DBC配置
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            // 判断DBC配置是否存在
            if (dbcConfig != null) {
                // 加载DBC文件
                List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
                Message message = null;
                DbcReader dbcReader = null;
                for (int i = 0; i < dbcReaders.size(); i++) {
                    // 查找目标报文和信号定义
                    message = dbcReaders.get(i).getBus().getMessageMap().get(messageName);
                    if (message != null) {
                        dbcReader = dbcReaders.get(i);
                        break;
                    }
                }

                // 报文不存在则抛出异常
                if (message == null) {
                    throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageName));
                }
                log.info("CAN通道{}报文{}信号{}物理值改为:{}", deviceChannel, messageName, signalName, signalPhyValue);
                // 获取信号执行任务
                CyclicTask cyclicTask = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel,
                        DataUtils.parseHexString(message.getId())));
                // 判断信号对应的周期发送任务是否存在
                if (cyclicTask == null) {  //信号没有运行，启动报文
                    byte[] bytesBefore = dbcReader.getMessageData(messageName);
                    log.info("CAN通道{}获取报文{}初始值:{}", deviceChannel, messageName, StrUtils.getHexStringWithBlank((bytesBefore)));
                    // 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
                    byte[] bytesAfter = decodeSignalPhyValue(message, signalName, BigDecimal.valueOf(signalPhyValue), bytesBefore);
                    sendCanData(deviceChannel, message.getId(), bytesAfter, message.getInterval(), cycle, message.isFd());
                } else if (cyclicTask instanceof ThreadBasedCyclicSendTask) { // 信号已经在运行
                    //信号已经在运行
                    ThreadBasedCyclicSendTask task = (ThreadBasedCyclicSendTask) cyclicTask;
                    byte[] bytesBefore = task.getCanMessage().getData();
                    // 自动处理物理值到原始值的转换（基于DBC中定义的缩放/偏移参数）
                    byte[] bytesAfter = decodeSignalPhyValue(message, signalName, BigDecimal.valueOf(signalPhyValue), bytesBefore);
                    // 修改运行中的报文数据，并恢复任务执行
                    task.modifyData(bytesAfter);
                    task.resume();
                } else {
                    throw new BusError("CAN报文任务类型错误");
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (BusError e) {
            throw e;
        } catch (Exception e) {
            throw new BusError(e);
        }
        return true;
    }

    @Override
    public void executeCycleStepLogic(String taskId, Integer deviceChannel, String ecuNodeName,
                                      String messageName, String signalName, Integer start,
                                      Integer end, int step, String interval) {
        try {
            ActualExpectedResult result = stepResults.get(taskId);
            if (result == null) return;

            // 初始化步进参数
            final long milliseconds = (long) (BaseRegexRule.getSecondsOfDefaultMills(interval) * 1000);
            int direction = Integer.signum(end - start);
            if (direction == 0) {
                result.put("setStepCanSignal", true, "步进初始值与终止值一致！");
                return;
            }

            int effectiveStep = step * direction;
            int currentValue = start;
            int stepCount = 0;

            // 步进主循环
            while (!Thread.currentThread().isInterrupted()) {
                while (direction == 1 ? currentValue <= end : currentValue >= end) {
                    // 暂停控制
                    handlePauseState();

                    // 执行信号设置
                    boolean success = setCanSignalPhyValue(deviceChannel, ecuNodeName,
                            messageName, signalName, currentValue, 1);
                    stepCount++;
                    result.put("stepCount", stepCount);
                    
                    if (!success) {
                        log.error("第{}次信号设置失败，终止任务", stepCount);
                        result.put("setStepCanSignal", false, "第" + stepCount + "次信号设置失败");
                        result.markComplete(false);
                        return;
                    }

                    result.put("setStepCanSignal", success, currentValue);

                    // 第二次步进成功后标记完成
                    if (stepCount >= 2) {
                        log.info("循环步进改变信号值成功，任务继续在后台运行");
                        result.put("setStepCanSignal", true, "循环步进改变信号值成功，任务继续在后台运行");
                    }

                    // 间隔控制
                    if (milliseconds > 0) {
                        Thread.sleep(milliseconds);
                    }

                    currentValue += effectiveStep;
                }

                // 循环重置
                currentValue = start;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("任务被中断");
            ActualExpectedResult result = stepResults.get(taskId);
            if (result != null) {
                result.put("setStepCanSignal", false, "任务被中断");
                result.markComplete(false);
            }
        } catch (BusError e) {
            log.error("总线错误: {}", e.getMessage());
            ActualExpectedResult result = stepResults.get(taskId);
            if (result != null) {
                result.put("setStepCanSignal", false, "总线错误: " + e.getMessage());
                result.markComplete(false);
            }
        }
    }

    // 封装的暂停处理方法
    private void handlePauseState() {
        while (isPause()) {
            log.info("CAN信号步进已暂停");
            synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                try {
                    ActionSequenceLock.getInstance().getPauseLock().wait();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }
    }


    private static byte[] decodeSignalPhyValue(Message message, String signalName, BigDecimal signalPhyValue, byte[] decodeBytes) throws DecodingFrameLengthException, BusError {
        Map<String, DecodedSignal> decodedSignalMap = message.decodeByDecodedSignal(decodeBytes);
        DecodedSignal decodedSignal = decodedSignalMap.get(signalName);
        if (decodedSignal == null) {
            throw new BusError(String.format("CAN报文%s信号%s未找到", message.getName(), signalName));
        }
        decodedSignal.changePhyValue(signalPhyValue);
        return message.encodeByDecodedSignal(decodedSignalMap);
    }

    private static byte[] decodeSignalRawValue(Message message, String signalName, BigInteger signalRawValue, byte[] decodeBytes) throws DecodingFrameLengthException, BusError {
        Map<String, DecodedSignal> decodedSignalMap = message.decodeByDecodedSignal(decodeBytes);
        DecodedSignal decodedSignal = decodedSignalMap.get(signalName);
        if (decodedSignal == null) {
            throw new BusError(String.format("CAN报文%s信号%s未找到", message.getName(), signalName));
        }
        decodedSignal.changeRawValue(signalRawValue);
        return message.encodeByDecodedSignal(decodedSignalMap);
    }

    @Override
    public double fetchCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws SimulatedDeviceNotification, BusError {
        return fetchCanSignalValue(deviceChannel, ecuNodeName, messageName, signalName, false);
    }

    @Override
    public double fetchCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws SimulatedDeviceNotification, BusError {
        return fetchCanSignalValue(deviceChannel, ecuNodeName, messageName, signalName, true);
    }

    private Message searchMessage(DbcConfig dbcConfig, String messageName) {
        List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
        Message message = null;
        for (DbcReader reader : dbcReaders) {
            message = reader.getBus().getMessageMap().get(messageName);
            if (message != null) {
                break;
            }
        }
        return message;
    }

    @Override
    public Map<String, DecodedSignal> fetchAllCanSignalValue(Integer deviceChannel, String messageName) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        try {
            if (dbcConfig != null) {
                Message message = searchMessage(dbcConfig, messageName);
                if (message == null) {
                    throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageName));
                }
                log.info("CAN通道{}读取{}报文:{}，设定读取超时:{}ms", deviceChannel, message.getName(), message.isFd() ? "CANFD" : "CAN", READ_TIMEOUT);
                byte[] data;
                if (isSimulated()) {
                    byte[] byteArray = new byte[64]; // Creating array with 64 bytes (B1 to B64)
                    byteArray[48] = (byte) 0x88; // B49
                    byteArray[49] = (byte) 0xFD; // B50
                    byteArray[50] = (byte) 0xEF; // B51
                    byteArray[51] = (byte) 0x41; // B52
                    byteArray[52] = (byte) 0x88; // B53
                    byteArray[53] = (byte) 0xFD; // B54
                    byteArray[54] = (byte) 0xEF; // B55
                    byteArray[55] = (byte) 0x41; // B56
                    byteArray[56] = (byte) 0x88; // B57
                    byteArray[57] = (byte) 0xFD; // B58
                    byteArray[58] = (byte) 0xEF; // B59
                    byteArray[59] = (byte) 0x41; // B60
                    byteArray[60] = (byte) 0x88; // B61
                    byteArray[61] = (byte) 0xFD; // B62
                    byteArray[62] = (byte) 0xEF; // B63
                    byteArray[63] = (byte) 0x41; // B64
                    data = byteArray;
//                    int length = message.getLength();
//                    data = new byte[length];
//                    // 填充数组
//                    for (int i = 0; i < length; i++) {
//                        data[i] = (byte) (i % 256);  // 使用模256的方式防止溢出
//                    }
                } else {
                    data = readDataByIdHex(deviceChannel, message.getIntegerId(), message.isFd(), READ_TIMEOUT);
                }
                log.info("读取到的数据:{}", ByteUtils.byteArrayToHexString(data));
                if (!ByteUtils.isEmpty(data)) {
                    return message.decodeByDecodedSignal(data);
                }
            } else {
                throw new BusError("dbc文件未找到");
            }
        } catch (DecodingFrameLengthException | TSCanException | InterruptedException e) {
            throw new BusError(e);
        }
        return new HashMap<>();
    }

    public double fetchCanSignalValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, boolean isPhyValue) throws SimulatedDeviceNotification, BusError {
        if (isSimulated()) {
            throw new SimulatedDeviceNotification();
        }
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            Map<String, DecodedSignal> decodedSignalMap = fetchAllCanSignalValue(deviceChannel, messageName);
            if (!decodedSignalMap.containsKey(signalName)) {
                throw new BusError(String.format("CAN通道%d报文%s信号%s未找到", deviceChannel, messageName, signalName));
            } else {
                return isPhyValue ? decodedSignalMap.get(signalName).getPhyValue().doubleValue() : decodedSignalMap.get(signalName).getRawValue().doubleValue();
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
    }

    /**
     * 批量获取指定CAN报文中多个信号的值
     *
     * @param deviceChannel CAN通道号(1/2)
     * @param ecuNodeName   ECU节点名称（当前参数未实际使用，保留供后续扩展）
     * @param messageName   报文名称（对应DBC文件中定义的报文名称）
     * @param signalNames   信号名称列表（对应DBC文件中定义的信号名称）
     * @param isPhyValue    是否获取物理值，true-获取物理值，false-获取原始值
     * @return Map<String, Double> 信号名称和对应值的映射关系
     * @throws SimulatedDeviceNotification 当设备处于模拟状态时抛出
     * @throws BusError                    当DBC文件未找到或信号不存在时抛出
     * @implNote 实现逻辑：
     * 1. 检查设备是否处于模拟状态，如果是则抛出SimulatedDeviceNotification异常
     * 2. 根据通道号获取DBC配置，加载对应DBC文件
     * 3. 调用fetchAllCanSignalValue方法获取指定报文的所有信号值
     * 4. 遍历signalNames列表，检查每个信号是否存在，如果不存在则抛出BusError异常
     * 5. 根据isPhyValue参数决定返回物理值还是原始值
     * 6. 将结果存入Map并返回
     */

    @Override
    public Map<String, Double> fetchCanSignalValues(Integer deviceChannel, String ecuNodeName, String messageName, List<String> signalNames, boolean isPhyValue) throws SimulatedDeviceNotification, BusError {
        if (isSimulated()) {
            throw new SimulatedDeviceNotification();
        }
        Map<String, Double> result = new HashMap<>();
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            Map<String, DecodedSignal> decodedSignalMap = fetchAllCanSignalValue(deviceChannel, messageName);
            for (String signalName : signalNames){
                if (!decodedSignalMap.containsKey(signalName)) {
                    throw new BusError(String.format("CAN通道%d报文%s信号%s未找到", deviceChannel, messageName, signalName));
                } else {
                    result.put(signalName, isPhyValue ? decodedSignalMap.get(signalName).getPhyValue().doubleValue() : decodedSignalMap.get(signalName).getRawValue().doubleValue());
                }
            }
            return result;
        } else {
            throw new BusError("dbc文件未找到");
        }
    }

    @Override
    public boolean setCanMessageCSRolling(Integer deviceChannel, String ecuNodeName, String messageNameOrID, int checksumStatus, int rollingCounterStatus) {
        return false;
    }

    @Override
    public boolean setCanMessageDLC(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double dlc) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            DbcReader dbcReader = null;
            for (DbcReader reader : dbcReaders) {
                message = reader.getBus().getMessageMap().get(messageNameOrId);
                if (message != null) {
                    dbcReader = reader;
                    break;
                }
            }
            if (message == null) {
                throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageNameOrId));
            }
            ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
            if (cyclicTask != null) {
                //信号已经在运行
                cyclicTask.modifyDlc((int) dlc);
            } else {
                try {
                    byte[] messageData = dbcReader.getMessageData(message.getName());
                    byte[] adjustedData = ByteUtils.adjustByteArray(messageData, (int) dlc);
                    sendCanData(deviceChannel, message.getId(), adjustedData, message.getInterval(), -1, message.isFd());
                } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                    throw new BusError(e);
                }
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return true;
    }

    @Override
    public boolean setCanMessageCycleTime(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double cycleTime) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            DbcReader dbcReader = null;
            for (DbcReader reader : dbcReaders) {
                message = reader.getBus().getMessageMap().get(messageNameOrId);
                if (message != null) {
                    dbcReader = reader;
                    break;
                }
            }
            if (message == null) {
                throw new BusError(String.format("CAN通道%d报文%s未找到", deviceChannel, messageNameOrId));
            }
            ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
            if (cyclicTask != null) {
                //信号已经在运行
                cyclicTask.modifyPeriod((float) (cycleTime / 1000.0f));
            } else {
                //报文未运行
                try {
                    byte[] messageData = dbcReader.getMessageData(message.getName());
                    sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), (int) cycleTime, message.isFd());
                } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                    throw new BusError(e);
                }
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return true;
    }

    @Override
    public boolean setCanSingleMsgStatus(Integer deviceChannel, String ecuNodeName, String messageNameOrId, int messageStatus) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            log.info("CAN通道{}报文{}{}", deviceChannel, messageNameOrId, messageStatus == 0 ? "丢失" : "恢复");
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            Message message = null;
            DbcReader dbcReader = null;
            for (DbcReader reader : dbcReaders) {
                // 根据id或者名称获取
                message = reader.getBus().getMessageMap().values().stream()
                        .filter(msg -> msg.getId().equals(messageNameOrId) || msg.getName().equals(messageNameOrId))
                        .findFirst()
                        .orElse(null);
                if (message != null) {
                    dbcReader = reader;
                    break;
                }
            }
            if (message == null) {
                throw new BusError(String.format("通道%d报文%s未找到", deviceChannel, messageNameOrId));
            }
            ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
            if (cyclicTask != null) {
                //信号已经在运行
                if (messageStatus == 0) {
                    cyclicTask.pause();
                } else {
                    cyclicTask.resume();
                }
            } else {
                //信号没在运行
                if (messageStatus == 1) {
                    try {
                        byte[] messageData = dbcReader.getMessageData(message.getName());
                        sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), null, message.isFd());
                    } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                        throw new BusError(e);
                    }
                }
            }
        } else {
            throw new BusError("dbc文件未找到");
        }
        return true;
    }

    //修改指定通道所有再发送的报文数据为数据库默认值
    @Override
    public boolean updateAllTasksWithData(Integer deviceChannel) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig == null) {
            throw new BusError("dbc文件未找到");
        }

        List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
        List<CyclicTask> tasks = getPeriodTasks(); // 获取所有周期任务
        log.info("当前任务总数：{}", tasks.size());

        for (CyclicTask task : tasks) {
            ChannelMessageId taskId = task.getTaskId();
            // 确保只处理当前通道的任务
            if (taskId.getChannel() != deviceChannel) {
                continue;
            }

            // 获取报文ID（转换为16进制字符串）
            String messageId = "0x" + String.format("%X", taskId.getArbitrationId());
            Message message = null;
            DbcReader dbcReader = null;

            // 在所有DBC文件中查找匹配的报文
            for (DbcReader reader : dbcReaders) {
//                System.out.println("reader.getBus().getHexMessageMap()" + reader.getBus().getHexMessageMap());
//                System.out.println("reader.getBus().getMessageMap() = " + reader.getBus().getMessageMap());
                for (Map.Entry<String, Message> entry : reader.getBus().getMessageMap().entrySet()) {
                    if (entry.getValue().getId().equals(messageId)) {
                        message = entry.getValue();
                        dbcReader = reader;
                        break;
                    }
                }
                if (message != null) {
                    break;
                }
            }

            if (message == null) {
                log.warn("通道{}未找到报文ID {} 的定义", deviceChannel, messageId);
                continue;
            }

            try {
                // 从DBC获取新数据
                byte[] messageData = dbcReader.getMessageData(message.getName());

                // 更新任务数据
                if (task instanceof ThreadBasedCyclicSendTask) {
                    ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) task;
                    cyclicTask.modifyData(messageData); // 修改任务数据
                    cyclicTask.resume(); // 确保任务继续发送
                }
            } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                throw new BusError(e);
            }
        }

        return true;
    }

    @Override
    public boolean setCanEcuAllMsgStatus(Integer deviceChannel, String ecuNodeName, int messageStatus) throws BusError {
        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(deviceChannel));
        if (dbcConfig != null) {
            log.info("CAN通道{}的ECU节点{}所有报文{}", deviceChannel, ecuNodeName, messageStatus == 0 ? "丢失" : "恢复");
            List<DbcReader> dbcReaders = DbcUtils.getDbcReaders(dbcConfig.getDbcPaths());
            List<Message> messages = new ArrayList<>();
            DbcReader dbcReader = null;
            for (DbcReader reader : dbcReaders) {
                messages = reader.getBus().getMessageByEcu(ecuNodeName);
                if (!messages.isEmpty()) {
                    dbcReader = reader;
                    break;
                }
            }
            for (Message message : messages) {
                ThreadBasedCyclicSendTask cyclicTask = (ThreadBasedCyclicSendTask) getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(deviceChannel, DataUtils.parseHexString(message.getId())));
                if (cyclicTask != null) {
                    //信号已经在运行
                    if (messageStatus == 0) {
                        cyclicTask.pause();
                    } else {
                        cyclicTask.resume();
                    }
                } else {
                    //信号没在运行
                    if (messageStatus == 1) {
                        try {
                            byte[] messageData = dbcReader.getMessageData(message.getName());
                            sendCanData(deviceChannel, message.getId(), messageData, message.getInterval(), null, message.isFd());
                        } catch (MessageNotFoundException | DecodingFrameLengthException e) {
                            throw new BusError(e);
                        }
                    }
                }
            }
            if (messages.isEmpty()) {
                log.info("CAN通道{}的ECU节点{}没包含需要发送的报文", deviceChannel, ecuNodeName);
            }
            return true;
        } else {
            throw new BusError("dbc文件未找到");
        }
    }


    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError {
        boolean isChecked = StrUtils.containsIgnoreCase(checkedContext, BaseRegexRule.PTX_RX_CONSTANT);
        String recvMessageId = StrUtils.extractHexId(checkedContext);
        return setCanPTS(deviceChannel, ecuNodeName, messageId, byteInstruction, recvMessageId, isChecked);
    }


    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String sendMessageId, String byteInstruction, String recvMessageId, boolean isChecked) throws BusError {
        try {
            String sendPtsMessageId = sendMessageId == null ? getDeviceConfig().getPtsConfig().getHexSendPtsMessageId() : sendMessageId;
            if (sendPtsMessageId == null) {
                log.warn("CAN PTS发送报文ID未设置");
                return false;
            }
            if (isChecked) {
                log.info("发送前清除CAN缓存");
                clearBuffer(deviceChannel);
            }
            log.info("CAN通道{}写入PTS ID:{}，报文数据:{}", deviceChannel, sendPtsMessageId, StrUtils.addHexSpace(byteInstruction));
            CanMessage canMessage = new CanMessage();
            canMessage.setChannel(deviceChannel);
            canMessage.setArbitrationId(DataUtils.parseHexString(sendPtsMessageId));
            canMessage.setData(ByteUtils.hexStringToByteArray(byteInstruction));
            canMessage.setCanFd(false);
            canMessage.setDlc(canMessage.getData().length);
            canMessage.setRemoteFrame(false);
            if (DataUtils.parseHexString(sendPtsMessageId) > 0x7FF) {
                canMessage.setExtendedId(true);
            } else {
                canMessage.setExtendedId(false);
            }
            canMessage.setSendTimes(1);
            send(canMessage);
            if (isChecked) {
                //先清空缓存队列
                canRecvQueueManager.clear();
                //需要检查PTS RX
                String receivePtsMessageId = recvMessageId == null ? getDeviceConfig().getPtsConfig().getHexReceivePtsMessageId() : recvMessageId;
                log.info("CAN通道{}发送完实时读取PTS ID:{}，设定读取超时时间:{}ms", deviceChannel, receivePtsMessageId, TIMEOUT_MILLISECONDS);
                int targetCanId = DataUtils.parseHexString(receivePtsMessageId);
                //读取PTS
                byte[] data = readDataByIdHex(deviceChannel, targetCanId, getDeviceConfig().getPtsConfig().isCanFd(), TIMEOUT_MILLISECONDS);
                canRecvQueueManager.put(deviceChannel, targetCanId, data);
                log.info("CAN通道{}完成读取PTS ID:{}", deviceChannel, ByteUtils.byteArrayToHexString(data));
            }
            return true;
        } catch (Throwable e) {
            throw new BusError(e);
        }
    }

    @Override
    public String fetchCanPTS(Integer deviceChannel, String messageId) throws BusError {
        try {
            String receivePtsMessageId = messageId == null ? getDeviceConfig().getPtsConfig().getHexReceivePtsMessageId() : messageId;
            boolean isCanFd = getDeviceConfig().getPtsConfig().isCanFd();
            if (receivePtsMessageId == null) {
                log.warn("CAN PTS接收报文ID未设置");
                return "";
            }
            log.info("CAN通道{}接收及判断PTS ID:{}", deviceChannel, receivePtsMessageId);
            int targetCanId = DataUtils.parseHexString(receivePtsMessageId);
            byte[] tsCanReturnData;
            if (canRecvQueueManager.contains(deviceChannel, targetCanId)) {
                //包含缓存队列中
                log.info("CAN通道{}已经缓存目标PTS接收ID:{}", deviceChannel, receivePtsMessageId);
                tsCanReturnData = canRecvQueueManager.get(deviceChannel, targetCanId);
            } else {
                log.info("CAN通道{}设定读取超时:{}ms", deviceChannel, TIMEOUT_MILLISECONDS);
                tsCanReturnData = readDataByIdHex(deviceChannel, targetCanId, isCanFd, TIMEOUT_MILLISECONDS);
            }
            if (tsCanReturnData != null) {
                log.info("CAN通道{}读取到PTS报文:{}{}", deviceChannel,
                        Arrays.toString(tsCanReturnData),
                        ByteUtils.isEmpty(tsCanReturnData) ? "" : String.format("(%s)", ByteUtils.byteArrayToHexString(tsCanReturnData)));
                return ByteUtils.byteArrayToHexString(tsCanReturnData);
            } else {
                log.error("CAN通道{}PTS接收报文ID:{}超时", deviceChannel, receivePtsMessageId);
                return "NA";
            }
        } catch (Throwable e) {
            throw new BusError(e);
        } finally {
            //清空缓冲队列
            canRecvQueueManager.clear();
        }

    }

    @Override
    public boolean fetchCanMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        return false;
    }

    @Override
    public boolean lastCheckCanMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        return false;
    }

    @Override
    public String notificationUpgrade(int fileType) throws BusError {
        return null;
    }

    @Override
    public boolean compareVersion(String ptsSwVersion) {
        return false;
    }

    @Override
    public double fetchXCPRX(String ecuNodeName, String xcpName) throws BusError {
        return 0;
    }

    @Override
    public boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpFunSwitch(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpVar(String varName, int xcpValue) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpSwitchAndVar(int switchCommand, String varName, int varValue) throws BusError {
        return false;
    }

    @Override
    public boolean setKeyPosition(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setKeyButton(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setRDefogSts(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setMirrorFoldSTS(String command) throws BusError {
        return false;
    }

    @Override
    public boolean setLampSwitch(String command) throws BusError {
        return false;
    }

    @Override
    public boolean checkTurnLamp(String turnLampType, int workTime, int checkPeriod) throws BusError {
        return false;
    }

    @Override
    public boolean checkFourDoor(String lockStatusCommand) throws BusError {
        return false;
    }

    @Override
    public boolean sendEventMsg(Integer deviceChannel, String MsgID, int msgTime, int msgCounter, String msgData) throws BusError {
        return false;
    }

    @Override
    public boolean checkVoltage(int pinNumber, int pinAliveTime, int pinNoAliveTime, int workCycleNumber) throws BusError {
        return false;
    }

    @Override
    public double fetchMsgCycleTime(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }

    @Override
    public int fetchMsgDLC(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }


    @Override
    public int fetchXcpVar(String varName) throws BusError {
        return 0;
    }

    @Override
    public boolean checkFindKeyOrNoKey(boolean findKey, int findKeyTime) throws BusError {
        return false;
    }

    public abstract int getMaxChannelCount();


    /**
     * 获取指定通道的最新CAN报文时间戳
     * @param deviceChannel 设备通道号
     * @return 最新时间戳（单位：毫秒）
     */
    public abstract long getLatestCanTimestamp(Integer deviceChannel);

    @Override
    public boolean wake(Integer deviceChannel, Integer time) {
        try {
            long startTime = System.currentTimeMillis();
            long lastTime = getLatestCanTimestamp(deviceChannel);

            while (System.currentTimeMillis() - startTime < time) {
                long newTime = getLatestCanTimestamp(deviceChannel);
                if (newTime > lastTime) {
                    return true; // 收到新报文
                }
                Thread.sleep(200); // 避免CPU空转
            }

            log.warn("在{}ms等待期内未收到新的CAN报文,设备唤醒失败", time);
            return false;
        } catch (InterruptedException e) {
            log.error("唤醒设备失败", e);
            return false;
        }
    }

    @Override
    public boolean sleep(Integer deviceChannel, Integer time) {
        try {
            long startTime = System.currentTimeMillis();
            long lastTime = getLatestCanTimestamp(deviceChannel);

            while (System.currentTimeMillis() - startTime < time) {
                long newTime = getLatestCanTimestamp(deviceChannel);
                if (newTime > lastTime) {
                    log.warn("在{}ms等待期内收到新的CAN报文,设备睡眠失败", time);
                    return false; // 收到新报文，睡眠失败
                }
                Thread.sleep(200); // 避免CPU空转
            }

            return true; // 成功进入睡眠状态

        } catch (InterruptedException e) {
            log.error("睡眠设备失败", e);
            return false;
        }
    }

    /**
     * 构建CAN报文基础信息
     *
     * @param deviceChannel 设备通道
     * @param messageId     报文ID
     * @param byteData      数据字符串（十六进制）
     * @return 构建好的CanMessage对象
     */
    protected CanMessage buildCanMessage(Integer deviceChannel, String messageId, String byteData) {
        CanMessage canMessage = new CanMessage();
        canMessage.setChannel(deviceChannel);
        canMessage.setArbitrationId(DataUtils.parseHexString(messageId));
        byte[] data = ByteUtils.hexStringToByteArray(byteData);

        // 填充数据：第一个字节为长度，后续为实际数据，不足部分补0
        byte[] paddedData = new byte[8];
        paddedData[0] = (byte) data.length;
        System.arraycopy(data, 0, paddedData, 1, data.length);
        Arrays.fill(paddedData, 1 + data.length, 8, (byte) 0x00);

        canMessage.setData(paddedData);
        canMessage.setCanFd(false);
        canMessage.setDlc(paddedData.length);
        canMessage.setRemoteFrame(false);

        if (DataUtils.parseHexString(messageId) > 0x7FF) {
            canMessage.setExtendedId(true);
        } else {
            canMessage.setExtendedId(false);
        }

        canMessage.setSendTimes(1);
        return canMessage;
    }

    private boolean onlySendSmallPacket(CanMessage canMessage, byte[] data, Integer deviceChannel) {
        try {
            byte[] paddedData = new byte[8];
            System.arraycopy(data, 0, paddedData, 0, data.length);
            Arrays.fill(paddedData, data.length, 8, (byte) 0x00);
            canMessage.setData(paddedData);
            canMessage.setDlc(paddedData.length);
            canMessage.setSendTimes(1);
            send(canMessage);
        } catch (Exception e) {
            log.error("发送失败: {}", e.getMessage());
            return false;
        }
        return true;
    }

    private boolean sendSmallPacket(CanMessage canMessage, byte[] data, Integer deviceChannel) {
        byte[] paddedData = new byte[8];
        System.arraycopy(data, 0, paddedData, 0, data.length);
        Arrays.fill(paddedData, data.length, 8, (byte) 0x00);
        canMessage.setData(paddedData);
        canMessage.setDlc(paddedData.length);
        canMessage.setSendTimes(1);

        try {
            send(canMessage);
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            int receiveId = -1;
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getResponseId())) {
                receiveId = Integer.parseInt(canConfig.getUdsModels().get(deviceChannel).getResponseId().replace("0x", ""), 16);
            }
            if(receiveId == -1){
                log.info("未配置响应ID无法判断是否收到首帧");
                return true;
            }
            Object buffer = getReceiveBuffer();
            Object msgData = null;
            if(getBufferSize(buffer) != 0){
                msgData  = readFromTail(buffer, 0);
            }

            long startTime = System.currentTimeMillis();
            boolean sendSuccess = false;

            while (System.currentTimeMillis() - startTime < 2000) {
                if (!isBufferEmpty(buffer)) {
                    for (int i = 0; i < getBufferSize(buffer); i++) {
                        Object returnData = readFromTail(buffer, i);
                        if(getTimestamp(returnData) >= getTimestamp(msgData)){
                            if (getDataFrameId(returnData) == receiveId && isValidFirstFrame(returnData)) {
                                byte[] flowControlFrame = new byte[8];
                                flowControlFrame[0] = 0x30;
                                Arrays.fill(flowControlFrame, 1, 8, (byte) 0x00);
                                sendFlowControlFrame(canMessage, flowControlFrame);
                                log.info("流控帧发送成功");
                                sendSuccess = true;
                                break;
                            } else if (getDataFrameId(returnData) == receiveId && !isValidFirstFrame(returnData)) {
                                log.info("收到非首帧回复");
                                sendSuccess = true;
                                break;
                            }
                        }else {
                            break;
                        }
                    }
                }
                if (sendSuccess) break;
                Thread.sleep(10);
            }
        } catch (Exception e) {
            log.error("发送失败: {}", e.getMessage());
            return false;
        }
        return true;
    }


    private boolean sendLargePacket(CanMessage canMessage, byte[] data, Integer deviceChannel) {
        int offset = 0;
        int packetCount = 0;

        byte[] firstFrameData = new byte[8];
        firstFrameData[0] = (byte) 0x10;
        System.arraycopy(data, offset, firstFrameData, 1, 7);
        canMessage.setData(firstFrameData);
        canMessage.setSendTimes(1);

        try {
            Object buffer = getReceiveBuffer();
            Object msgData = null;
            if(getBufferSize(buffer) != 0){
                msgData = readFromTail(buffer, 0);
            }
            send(canMessage);
            offset += 7;

            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            int receiveId = -1;
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getResponseId())) {
                receiveId = Integer.parseInt(canConfig.getUdsModels().get(deviceChannel).getResponseId().replace("0x", ""), 16);
            }

            long startTime = System.currentTimeMillis();

            while (System.currentTimeMillis() - startTime < 3000) {
                if (!isBufferEmpty(buffer)) {
                    for (int i = 0; i < getBufferSize(buffer); i++) {
                        Object returnData = readFromTail(buffer, i);
                        if (isValidFlowControlFrame(returnData, receiveId, msgData)) {
                            int packetsToSend = getDataBytes(returnData)[1];
                            int remainingPackets = (data.length - offset + 6) / 7;
                            int actualPacketsToSend = (packetsToSend == 0) ? remainingPackets : Math.min(packetsToSend, remainingPackets);

                            for (int j = 0; j < actualPacketsToSend && offset < data.length; j++) {
                                int headerValue = 0x21 + ((packetCount++) % 0x10);
                                int dataSize = Math.min(data.length - offset, 7);
                                byte[] packetData = new byte[dataSize + 1];
                                packetData[0] = (byte) headerValue;
                                System.arraycopy(data, offset, packetData, 1, dataSize);

                                if (packetData.length < 8) {
                                    byte[] paddedData = new byte[8];
                                    System.arraycopy(packetData, 0, paddedData, 0, packetData.length);
                                    Arrays.fill(paddedData, packetData.length, 8, (byte) 0x00);
                                    packetData = paddedData;
                                }

                                canMessage.setData(packetData);
                                canMessage.setSendTimes(1);
                                send(canMessage);
                                offset += dataSize;
                            }

                            if (offset >= data.length) {
                                return true;
                            }
                            startTime = System.currentTimeMillis(); // 重置超时计时器
                        } else if (isContinueFrame(returnData)) {
                            startTime = System.currentTimeMillis();
                        } else if (isAbortFrame(returnData)) {
                            return false;
                        }
                    }
                }
                Thread.sleep(10);
            }
            return false;
        } catch (Exception e) {
            log.error("发送失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取接收缓冲区对象
     * @return 子类实现返回对应的数据结构
     */
    public abstract Object getReceiveBuffer();

    /**
     * 获取接收缓冲区对象
     * @return 子类实现返回对应的数据结构
     */
    public abstract Object getAllReceiveBuffer();

    /**
     * 从接收缓冲区读取数据
     * @param buffer 缓冲区对象
     * @return 返回具体的数据对象（如 TSCanReturnData 或 CanMessageVo）
     */
    public abstract Object readFromTail(Object buffer, int index);

    /**
     * 从接收缓冲区读取数据
     * @param buffer 缓冲区对象
     * @return 返回具体的数据对象（如 TSCanReturnData 或 CanMessageVo）
     */
    public abstract Object readFromAllTail(Object buffer, int index);

    /**
     * 获取数据帧ID
     * @param data 数据帧对象
     * @return CAN ID
     */
    public abstract int getDataFrameId(Object data);

    /**
     * 获取时间戳
     * @param data 数据帧对象
     * @return 时间戳
     */
    public abstract long getTimestamp(Object data);

    /**
     * 获取报文方向
     * @param data 数据帧对象
     * @return 报文方向
     */
    public abstract String getDirection(Object data);

    /**
     * 获取数据字节数组
     * @param data 数据帧对象
     * @return 字节数组
     */
    public abstract byte[] getDataBytes(Object data);

    /**
     * 判断是否是错误帧
     * @param data 数据帧对象
     * @return boolean
     */
    public abstract boolean isErrorFream(Object data);

    /**
     * 判断缓冲区是否为空
     * @param buffer 缓冲区对象
     * @return 是否为空
     */
    public abstract boolean isBufferEmpty(Object buffer);

    /**
     * 判断缓冲区是否为空
     * @param buffer 缓冲区对象
     * @return 是否为空
     */
    public abstract boolean isBufferAllEmpty(Object buffer);

    /**
     * 获取缓冲区大小
     * @param buffer 缓冲区对象
     * @return 缓冲区条目数量
     */
    public abstract int getBufferSize(Object buffer);

    /**
     * 获取缓冲区大小
     * @param buffer 缓冲区对象
     * @return 缓冲区条目数量
     */
    public abstract int getAllBufferSize(Object buffer);

    /**
     * 构造并发送流控帧
     * @param canMessage 基础CAN消息
     * @param flowControlFrame 流控帧内容
     */
    public abstract void sendFlowControlFrame(CanMessage canMessage, byte[] flowControlFrame) throws BusError;

    protected abstract boolean isValidFirstFrame(Object data);
    protected abstract boolean isValidFlowControlFrame(Object data, int receiveId, Object msgData);
    protected abstract boolean isContinueFrame(Object data);
    protected abstract boolean isAbortFrame(Object data);

    public OperationResult setCanUDS27ServerFun(UDS27 model){
        OperationResult operationResult = new OperationResult();
        boolean pass = false;
        try {
            pass = setCanUDS27ServerFun(model.getChannel(), model.getRequestId().trim(), model.getRequestPdu().trim());
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中UDS 27服务，指令,检测结果:{}", pass ? "通过" : "失败");
        operationResult.setMessage(pass ? "27通过" : "发送失败可查看后台信息和报文");
        operationResult.setOk(pass);
        return operationResult;
    }

    public boolean setCanUDS27ServerFun(int deviceChannel, String address, String udsCaseIdString) throws BusError{
        String messageId = "";
        String responseId = ""; // 用于存储响应ID
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if ("Fun".equalsIgnoreCase(address)) {
            if (canConfig.getUdsModels().get(deviceChannel).isEnableFunction() && StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getFunctionId()) && StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getResponseId())) {
                messageId = canConfig.getUdsModels().get(deviceChannel).getFunctionId().replace("0x", "");
                responseId = canConfig.getUdsModels().get(deviceChannel).getResponseId().replace("0x", "");; // 功能寻址时，响应ID与请求ID相同
            } else {
                return false;
            }
        } else if ("Phy".equalsIgnoreCase(address)) {
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getRequestId()) && StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getResponseId())) {
                messageId = canConfig.getUdsModels().get(deviceChannel).getRequestId().replace("0x", "");
                responseId = canConfig.getUdsModels().get(deviceChannel).getResponseId().replace("0x", ""); // 物理寻址时，使用配置的响应ID
            } else {
                return false;
            }
        } else {
            messageId = address.replace("0x", "");
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getResponseId())) {
                responseId = canConfig.getUdsModels().get(deviceChannel).getResponseId().replace("0x", ""); // 物理寻址时，使用配置的响应ID
            } else {
                return false;
            }
        }
        String sendKeyData = udsCaseIdString;
        String sendRequestData = udsCaseIdString;
        int securityLevel = 0;
        int[] actualKeyArraySize = new int[1]; // 使用数组模拟引用传递
        if (udsCaseIdString.startsWith("27")) {
            try {
                int num = Integer.parseInt(udsCaseIdString.substring(2), 16);
                if (num % 2 == 0) {
                    // 偶数，保存为sendKeyData，同时生成奇数编号的sendRequestData
                    sendKeyData = "27" + String.format("%02X", num);
                    num -= 1;
                    sendRequestData = "27" + String.format("%02X", num);
                    securityLevel = num;
                } else {
                    // 奇数，保存为sendRequestData，同时生成偶数编号的sendKeyData
                    sendRequestData = "27" + String.format("%02X", num);
                    securityLevel = num;
                    num += 1;
                    sendKeyData = "27" + String.format("%02X", num);
                }
                // 根据安全等级规则设置安全访问等级
                log.info("UDS 27服务请求，安全等级: {}", securityLevel);
            } catch (NumberFormatException e) {
                log.warn("Invalid UDS case ID format: {}", udsCaseIdString);
            }
        }
        sendDatas(deviceChannel, messageId, sendRequestData);
        String data = checkReplyData(deviceChannel, responseId, "");
        if (data == null || data.isEmpty()) {
            log.info("未获取到正确的seed");
            return false;
        }
        log.info("获取到正确的seed" + data);
        // 将数据转换为字节数组
        byte[] bytes = ByteUtils.hexStringToByteArray(data);
        if (bytes.length > 0) {
            int keyStartIndex = 2;
            if (keyStartIndex <= bytes.length) {
                // 提取key部分的字节
                byte[] seedBytes = Arrays.copyOfRange(bytes, keyStartIndex, bytes.length);
                // 将key字节转换为十六进制字符串

                boolean loadStatus = SeedKeyUtils.loadDll(canConfig.getUdsModels().get(deviceChannel).getSeedKeyPath());
                if (!loadStatus) {
                    log.error("加载DLL失败");
                    return false;
                }
                SeedKeyUtils seedKeyUtils = new SeedKeyUtils();
                byte[] generatedKey = seedKeyUtils.generateKey(seedBytes, seedBytes.length, securityLevel, "Standard");
                if (generatedKey == null && generatedKey.length == 0) {
                    log.error("获取的key为空");
                    return false;
                }
                String key = ByteUtils.byteArrayToHexString(generatedKey);
                log.error("获取到key{}",key);
                // 发送偶数27服务加上key
                String keyKata = sendKeyData + key;
                // 假设使用默认物理寻址发送服务
                sendDatas(deviceChannel, messageId, keyKata);
            }
        }
        return true;
    }

    public boolean setCanUDSFun(int deviceChannel,String address, String udsCaseIdString) throws BusError{
        String messageId = "";
        if(address.equals("Fun")){
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (canConfig.getUdsModels().get(deviceChannel).isEnableFunction() && StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getFunctionId())) {
                messageId = canConfig.getUdsModels().get(deviceChannel).getFunctionId();
            }else {
                return false;
            }
        }else if(address.equals("Phy")){
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getRequestId())) {
                messageId = canConfig.getUdsModels().get(deviceChannel).getRequestId().replace("0x", "");
            }else{
                return false;
            }
        }else{
            messageId = address;
        }
        return sendDatas(deviceChannel, messageId, udsCaseIdString);
    }

    public boolean sendDatas(Integer deviceChannel, String messageId, String byteData){
        CanMessage canMessage = new CanMessage();
        canMessage.setChannel(deviceChannel);
        canMessage.setArbitrationId(DataUtils.parseHexString(messageId));
        byte[] newData = ByteUtils.hexStringToByteArray(byteData);
        // 创建新的数据数组，长度为原数据长度+1，用于在第一个字节存储长度
        byte[] data = new byte[newData.length + 1];
        data[0] = (byte) newData.length;
        System.arraycopy(newData, 0, data, 1, newData.length);
        canMessage.setCanFd(false);
        canMessage.setRemoteFrame(false);
        if (DataUtils.parseHexString(messageId) > 0x7FF) {
            canMessage.setExtendedId(true);
        } else {
            canMessage.setExtendedId(false);
        }
        // 判断数据长度
        if (data.length <= 8) {
            return sendSmallPacket(canMessage, data, deviceChannel);
        } else {
            return sendLargePacket(canMessage, data, deviceChannel);
        }
    }


    /**
     * 检查是否回复数据
     * @param deviceChannel 设备通道
     * @param targetId 消息ID
     * @param buffer 期望数据
     * @return
     */
    private String expectedDataIsNull(int deviceChannel, int targetId, Object buffer){
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < 3000) {
            // 只检查最后一帧是否为目标ID和通道
            if (getBufferSize(buffer) > 0) {
                Object dataObj = readFromTail(buffer, 0);
                if (getDataFrameId(dataObj) == targetId && getChannelFromData(dataObj) == deviceChannel) {
                    return "";
                }
            }
            try {
                Thread.sleep(100); // 等待100毫秒再次检查
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return "";
            }
        }
        return "NULL";
    }

    /**
     * 检查是否回复数据，数据长度小于8字节
     * @param deviceChannel 设备通道
     * @param targetId 消息ID
     * @return
     */
    private String expectedDataLessThan8Bytes(int deviceChannel, int targetId, Object buffer){
        for (int i = 0; i < getBufferSize(buffer); i++) {
            Object dataObj = readFromTail(buffer, i);
            if (getDataFrameId(dataObj) == targetId && getChannelFromData(dataObj) == deviceChannel) {
                byte[] dataBytes = getDataBytes(dataObj);
                int dataLength = dataBytes[0] & 0xFF; // 第一个字节为有效数据长度
                int startIndex = 1; // 从第二个字节开始提取
                int endIndex = Math.min(startIndex + dataLength, dataBytes.length); // 防止数组越界
                byte[] actualData = Arrays.copyOfRange(dataBytes, startIndex, endIndex);
                return ByteUtils.byteArrayToHexString(actualData);
            }
        }
        return "";
    }

    /**
     * 检查是否回复数据，数据长度大于8字节
     * @param deviceChannel 设备通道
     * @param targetId 消息ID
     * @return
     */
    private String expectedDataGreaterThan8Bytes(int deviceChannel, int targetId, Object buffer){
        // 数据长度大于等于8，进行分包处理
        List<byte[]> dataFrames = new ArrayList<>();
        int totalLength = -1;
        long startTime = System.currentTimeMillis();
        
        // 寻找多帧
        while (System.currentTimeMillis() - startTime < 300) { // 设置300毫秒超时
            dataFrames.clear(); // 清空之前的数据
            totalLength = -1;
            
            for (int i = 0; i < getBufferSize(buffer); i++) {
                Object currentData = readFromTail(buffer, i);
                if (getChannelFromData(currentData) == deviceChannel && getDataFrameId(currentData) == targetId) {
                    byte[] data = getDataBytes(currentData);
                    if (data.length >= 1) {
                        int frameHeader = data[0] & 0xFF;
                        // 处理首帧（0x10）
                        if (frameHeader == 0x10) {
                            // 首帧的第二个字节是数据总长度
                            totalLength = data[1] & 0xFF;
                            byte[] packetData = new byte[6];
                            System.arraycopy(data, 2, packetData, 0, 6);
                            dataFrames.add(0, packetData);
                            break;
                        }
                        // 处理后续数据帧（0x21~0x2F）
                        else if (frameHeader >= 0x21 && frameHeader <= 0x2F) {
                            int frameIndex = (frameHeader - 0x21) + 1; // 21开头的放在索引1位置
                            int dataLength = Math.min(data.length - 1, 7); // 最多7个数据字节
                            byte[] packetData = new byte[dataLength];
                            System.arraycopy(data, 1, packetData, 0, dataLength);
                            // 确保列表足够大以容纳此帧
                            while (dataFrames.size() <= frameIndex) {
                                dataFrames.add(null);
                            }
                            dataFrames.set(frameIndex, packetData);
                        }
                    }
                }
            }
            // 如果没有找到首帧，无法确定总长度
            if (totalLength == -1) {
                try {
                    Thread.sleep(20); // 等待20ms再尝试
                    continue;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return "";
                }
            }
            // 计算已接收的数据长度
            int receivedLength = 0;
            for (byte[] packet : dataFrames) {
                if (packet != null) {
                    receivedLength += packet.length;
                }
            }
            // 如果收到的数据长度等于总长度，跳出循环
            if (receivedLength >= totalLength) {
                break;
            }
            try {
                Thread.sleep(20); // 等待20ms再尝试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return "";
            }
        }
        // 合并所有数据帧
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            // 遍历所有数据帧并按顺序写入（首帧已在正确位置）
            for (byte[] packet : dataFrames) {
                if (packet != null) {
                    outputStream.write(packet);
                }
            }
            // 根据首帧指示的总长度截取数据
            byte[] receivedData = outputStream.toByteArray();
            if (receivedData.length > totalLength) {
                receivedData = Arrays.copyOf(receivedData, totalLength);
            }
            // 返回实际数据部分的十六进制字符串
            return ByteUtils.byteArrayToHexString(receivedData);
        } catch (IOException e) {
            log.error("合并数据帧时发生错误", e);
            return "";
        }
    }
    /**
     * 父类中的公共方法：检查回复数据是否符合预期（支持单帧/多帧）
     * @param deviceChannel 设备通道号
     * @param messageId     报文ID（如 "0x7F"）
     * @param expectedLength  预期的完整数据字节数组（包含长度信息）
     * @param buffer        接收缓冲区（由子类提供）
     * @return 是否匹配
     */
    protected String checkReplyDataCommonLogic(int deviceChannel, String messageId, int expectedLength, Object buffer) {
        int targetId = -1;
        if(!messageId.isEmpty()){
            targetId = Integer.parseInt(messageId.replace("0x", ""), 16);
        }else{
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getResponseId())) {
                targetId = Integer.parseInt(canConfig.getUdsModels().get(deviceChannel).getResponseId().replace("0x", ""),16);
            }else{
                return "";
            }
        }
        log.info("接收到的报文条数:{}",getBufferSize(buffer));
        if(expectedLength == -1){
            log.info("进入检查是否回复数据");
            return expectedDataIsNull(deviceChannel, targetId, buffer);
        } else if (expectedLength == 0) {
            log.info("进入无法知晓场长度的数据获取");
            // 当expectedData长度为0时，检查是否收到首帧(0x10)或连续帧(0x21-0x2F)
            for (int i = 0; i < getBufferSize(buffer); i++) {
                Object dataObj = readFromTail(buffer, i);
                if (getDataFrameId(dataObj) == targetId && getChannelFromData(dataObj) == deviceChannel) {
                    byte[] actualData = getDataBytes(dataObj);
                    if (actualData.length > 0) {
                        int pci = actualData[0] & 0xFF;
                        // 如果是首帧(0x10)或连续帧(0x21-0x2F)，则认为是大于8字节的情况
                        if (pci == 0x10 || (pci >= 0x21 && pci <= 0x2F)) {
                            return expectedDataGreaterThan8Bytes(deviceChannel, targetId, buffer);
                        }else{
                            // 如果没有找到多帧数据，则按小于8字节处理
                            return expectedDataLessThan8Bytes(deviceChannel, targetId, buffer);
                        }
                    }
                }
            }
        } else {
            if (expectedLength < 8) {
                log.info("进入小于8字节数据获取");
                return expectedDataLessThan8Bytes(deviceChannel, targetId, buffer);
            } else {
                log.info("进入大于8字节数据获取");
                return expectedDataGreaterThan8Bytes(deviceChannel, targetId, buffer);
            }
        }
        return "";
    }
    public abstract int getChannelFromData(Object data);

    public abstract String checkReplyData(Integer deviceChannel, String messageId, String byteData);

    public abstract String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError;

    public abstract void setUdsConfig(Integer deviceChannel);

    public OperationResult responsiveServices(UdsModel udsModel){
        log.info("开始响应服务");
        boolean result = false;
        OperationResult operationResult = new OperationResult();
        if(udsModel.isEnableFunction() && StringUtils.isNotBlank(udsModel.getFunctionId())){
            if(StringUtils.isNotBlank(udsModel.getServiceData())) {
                result = sendDatas(udsModel.getChannel(), udsModel.getFunctionId(), udsModel.getServiceData());
                String resUdsData = null;
                operationResult.setMessage("发送服务成功");
                if (StringUtils.isNotBlank(udsModel.getResponseId()) && StringUtils.isNotBlank(udsModel.getResponseData())) {
                    resUdsData = checkReplyData(udsModel.getChannel(), udsModel.getResponseId(), udsModel.getResponseData());
                    result = resUdsData.equals(udsModel.getResponseData());
                    if(resUdsData.isEmpty()){
                        operationResult.setMessage("发送服务成功，未获取到响应");
                    }else{
                        operationResult.setData(resUdsData);
                        if(result){
                            operationResult.setMessage("发送服务成功，获取到正确响应");
                        }else{
                            operationResult.setMessage("发送服务成功，未获取到正确响应");
                        }
                    }
                }
                operationResult.setOk(result);
                return operationResult;
            }else{
                log.info("服务数据为空发送服务失败");
                operationResult.setMessage("服务数据为空发送服务失败");
                operationResult.setOk(false);
                return operationResult;
            }
        }else{
            if (StringUtils.isNotBlank(udsModel.getServiceData()) && StringUtils.isNotBlank(udsModel.getRequestId())) {
                result = sendDatas(udsModel.getChannel(), udsModel.getRequestId(), udsModel.getServiceData());
                String resUdsData = null;
                operationResult.setMessage("服务发送成功");
                if (StringUtils.isNotBlank(udsModel.getResponseData()) && StringUtils.isNotBlank(udsModel.getResponseId())) {
                    resUdsData = checkReplyData(udsModel.getChannel(), udsModel.getResponseId(), udsModel.getResponseData());
                    result = resUdsData.equals(udsModel.getResponseData());
                    if(resUdsData.isEmpty()){
                        operationResult.setMessage("发送服务成功，未获取到响应");
                    }else{
                        operationResult.setData(resUdsData);
                        if(result){
                            operationResult.setMessage("发送服务成功，获取到正确响应");
                        }else{
                            operationResult.setMessage("发送服务成功，未获取到正确响应");
                        }
                    }
                }
                operationResult.setOk(result);
                return operationResult;
            }else{
                log.info("服务数据或请求ID为空发送服务失败");
                operationResult.setMessage("服务数据或请求ID为空发送服务失败");
                operationResult.setOk(false);
                return operationResult;
            }
        }
    }

    public boolean setCanUdsKeepSession(int deviceChannel, String status) throws BusError{
        boolean state = status.equalsIgnoreCase("Start");
        return send3EService(deviceChannel,state);
    }

    /**
     * 发送3E服务
     * @param deviceChannel 设备通道
     * @param isOpen 是否开启功能寻址
     * @return 是否成功
     */
    public boolean send3EService(Integer deviceChannel,boolean isOpen) {
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        UdsModel udsModel = canConfig.getUdsModels().get(deviceChannel);

        String messageId;
        byte[] byteData;

        // 判断是否开启功能寻址
        if (udsModel.isEnableFunction()) {
            // 功能寻址ID
            messageId = udsModel.getFunctionId();
        } else {
            messageId = udsModel.getRequestId();
        }
        if (StringUtils.isBlank(messageId)) {
            return false;
        }
        // 默认响应数据s
        if (udsModel.isEnableResponse()) {
            byteData = new byte[]{0x02, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
        } else {
            byteData = new byte[]{0x02, 0x3E, (byte) 0x80, 0x00, 0x00, 0x00, 0x00, 0x00};
        }
        if (isOpen) {
            // 使用接口中的方法发送CAN报文
            CanMessage canMessage = new CanMessage();
            canMessage.setChannel(deviceChannel);
            canMessage.setArbitrationId(DataUtils.parseHexString(messageId));

            // 填充数据：第一个字节为长度，后续为实际数据，不足部分补0
            canMessage.setData(byteData);
            canMessage.setCanFd(false);
            canMessage.setDlc(8);
            canMessage.setRemoteFrame(false);

            canMessage.setExtendedId(DataUtils.parseHexString(messageId) > 0x7FF);
            canMessage.setPeriod(4F);
            canMessage.setSendTimes(-1);
            canMessage.setFramesPerSendNum(-1);
            sendPeriodic(canMessage, 4F,-1F);
        }else{
            stopCanMessage(deviceChannel,messageId);
        }
        return true;
    }

    @Override
    public ActualExpectedResult canUdsFunNotDTCInfo(int deviceChannel,String expectResult){
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean exist = getAndcheckDtc(deviceChannel, expectResult);
        log.info("fetchCanUdsFunNotDTC期望:{},检测结果:{},共耗时:{}毫秒", expectResult, !exist ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanUdsFunInfo", !exist, !exist ? "无期望DTC" : expectResult);
        return actualExpectedResult;
    }

    @Override
    public ActualExpectedResult canUdsFunWithDTCInfo(int deviceChannel,String expectResult){
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean exist = getAndcheckDtc(deviceChannel, expectResult);
        log.info("获取fetchCanUdsFunWithDTC期望:{},检测结果:{},共耗时:{}毫秒", expectResult, exist ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanUdsFunWithDTCInfo", exist, exist ? expectResult : "无期望DTC" );
        return actualExpectedResult;
    }

    public boolean getAndcheckDtc(int deviceChannel,String dtc){
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        UdsModel udsModel = canConfig.getUdsModels().get(deviceChannel);
        String messageId = null;
        if (StringUtils.isNotBlank(udsModel.getResponseId())) {
            messageId = udsModel.getResponseId().replace("0x", "");
        } else {
            // 其他情况直接返回false
            log.warn("未获取到有效的响应ID，无法执行UDS服务");
            return false;
        }
        String result = checkReplyData(deviceChannel, messageId, "");
        return checkDtc(result,dtc);
    }


    public boolean checkDtc(String result, String dtc) {
        if (result == null || dtc == null || result.isEmpty()) {
            return false;
        }

        // 去除result前三个字节(6个字符)
        if (result.length() < 6) {
            return false;
        }
        String trimmedResult = result.substring(6);

        // 如果去除前三个字节后为空，则返回false
        if (trimmedResult.isEmpty()) {
            return false;
        }

        // 将trimmedResult按每组4个字节(8个字符)拆分
        List<String> dtcGroups = new ArrayList<>();
        for (int i = 0; i < trimmedResult.length(); i += 8) {
            int endIndex = Math.min(i + 8, trimmedResult.length());
            dtcGroups.add(trimmedResult.substring(i, endIndex));
        }

        // 获取传入dtc的DTC码和状态
        String targetDtcCode;
        String targetDtcStatus = null;
        
        // dtc参数至少需要3个字节(6个字符)
        if (dtc.length() < 6) {
            return false;
        }
        
        targetDtcCode = dtc.substring(0, 6);
        if (dtc.length() >= 8) {
            targetDtcStatus = dtc.substring(6, 8);
        }

        // 遍历result拆出的DTC组进行匹配
        for (String group : dtcGroups) {
            // 每组至少需要3个字节(6个字符)来构成DTC
            if (group.length() >= 6) {
                String dtcCode = group.substring(0, 6);
                if (dtcCode.equalsIgnoreCase(targetDtcCode)) {
                    // DTC匹配成功，检查状态
                    if (targetDtcStatus != null && group.length() >= 8) {
                        String dtcStatus = group.substring(6, 8);
                        if (dtcStatus.equalsIgnoreCase(targetDtcStatus)) {
                            return true;
                        }
                    } else if (targetDtcStatus == null) {
                        // 不需要检查状态
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public abstract long getSigTimeByMsg(int deviceChannel, Object canMessage, String sigName, String sigValue);

    @Override
    public String checkMsgTime(int deviceChannel,String requestId,String responseId,String expectTime,String errorTime){
        Object buffer = getReceiveBuffer();
        for (int i = 0; i < getBufferSize(buffer); i++) {
            Object dataObj = readFromTail(buffer, i);
            int reqId = Integer.parseInt(requestId.replace("0x", ""), 16);
            int resId = Integer.parseInt(responseId.replace("0x", ""), 16);
            long reqTimestamp = -1;
            long resTimestamp = -1;

            // 查找requestId或responseId的报文
            for (int j = 0; j < getBufferSize(buffer); j++) {
                Object msgData = readFromTail(buffer, j);
                int currentId = getDataFrameId(msgData);
                if (getChannelFromData(msgData) == deviceChannel) {
                    if (currentId == reqId) {
                        reqTimestamp = getTimestamp(msgData);
                        break;
                    } else if (currentId == resId) {
                        resTimestamp = getTimestamp(msgData);
                        break;
                    }
                }
            }

            // 如果找到了requestId或responseId中的一个，则查找另一个
            if (reqTimestamp != -1) {
                for (int j = 0; j < getBufferSize(buffer); j++) {
                    Object msgData = readFromTail(buffer, j);
                    if (getChannelFromData(msgData) == deviceChannel && getDataFrameId(msgData) == resId) {
                        resTimestamp = getTimestamp(msgData);
                        break;
                    }
                }
            } else if (resTimestamp != -1) {
                for (int j = 0; j < getBufferSize(buffer); j++) {
                    Object msgData = readFromTail(buffer, j);
                    if (getChannelFromData(msgData) == deviceChannel && getDataFrameId(msgData) == reqId) {
                        reqTimestamp = getTimestamp(msgData);
                        break;
                    }
                }
            }

            // 如果两个时间戳都找到了，则计算差值
            if (reqTimestamp != -1 && resTimestamp != -1) {
                long timeDiff = Math.abs(resTimestamp - reqTimestamp);
                return String.valueOf(timeDiff);
            }
        }
        return "";
    }

    @Override
    public String checkMsgSigTime(int deviceChannel, String requestId, String signalName, String signalValue, String SignalValue1, String expectTime, String errorTime) {
        Object buffer = getReceiveBuffer();
        int reqId = Integer.parseInt(requestId.replace("0x", ""), 16);
        long reqSigTime = -1;
        long resSigTime = -1;

        // 一次循环查找requestId报文且信号值等于signalValue和SignalValue1
        for (int i = 0; i < getBufferSize(buffer); i++) {
            Object msgData = readFromTail(buffer, i);
            if (getChannelFromData(msgData) == deviceChannel && getDataFrameId(msgData) == reqId) {

                // 只有当reqSigTime尚未找到时才尝试获取
                if (reqSigTime == -1) {
                    reqSigTime = getSigTimeByMsg(deviceChannel, msgData, signalName, signalValue);
                }
                
                // 只有当resSigTime尚未找到时才尝试获取
                if (resSigTime == -1) {
                    resSigTime = getSigTimeByMsg(deviceChannel, msgData, signalName, SignalValue1);
                }
                
                // 如果两个信号时间都找到了，则提前退出循环
                if (reqSigTime != -1 && resSigTime != -1) {
                    break;
                }
            }
        }
        // 如果两个信号时间都找到了，则计算差值（大的减小的）
        if (reqSigTime != -1 && resSigTime != -1) {
            long timeDiff = Math.max(reqSigTime, resSigTime) - Math.min(reqSigTime, resSigTime);
            return String.valueOf(timeDiff);
        }
        return "";
    }

    public boolean sendService(Integer deviceChannel, String messageId, String byteData){
        CanMessage canMessage = new CanMessage();
        canMessage.setChannel(deviceChannel);
        canMessage.setArbitrationId(DataUtils.parseHexString(messageId));
        byte[] newData = ByteUtils.hexStringToByteArray(byteData);
        // 创建新的数据数组，长度为原数据长度+1，用于在第一个字节存储长度
        byte[] data = new byte[newData.length + 1];
        data[0] = (byte) newData.length;
        System.arraycopy(newData, 0, data, 1, newData.length);
        canMessage.setCanFd(false);
        canMessage.setRemoteFrame(false);
        if (DataUtils.parseHexString(messageId) > 0x7FF) {
            canMessage.setExtendedId(true);
        } else {
            canMessage.setExtendedId(false);
        }
        // 判断数据长度
        if (data.length <= 8) {
            return onlySendSmallPacket(canMessage, data, deviceChannel);
        } else {
            return sendLargePacket(canMessage, data, deviceChannel);
        }

    }

    @Override
    public boolean canUdsContinuous(Integer deviceChannel, String messageId, String byteData, String time, String secondMessageId, String secondByteData){
        String oneAddress = messageId;
        String twoAddress = secondMessageId;
        if(oneAddress.equals("Fun")){
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (canConfig.getUdsModels().get(deviceChannel).isEnableFunction() && StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getFunctionId())) {
                oneAddress = canConfig.getUdsModels().get(deviceChannel).getFunctionId();
            }else {
                return false;
            }
        }else if(oneAddress.equals("Phy")){
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getRequestId())) {
                oneAddress = canConfig.getUdsModels().get(deviceChannel).getRequestId().replace("0x", "");
            }else{
                return false;
            }
        }else{
            oneAddress = messageId;
        }
        boolean sendOne = sendService(deviceChannel, oneAddress, byteData);
        try{
            Thread.sleep(Integer.parseInt(time));
        }catch (InterruptedException e){
            e.printStackTrace();
        }
        if(twoAddress.equals("Fun")){
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (canConfig.getUdsModels().get(deviceChannel).isEnableFunction() && StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getFunctionId())) {
                twoAddress = canConfig.getUdsModels().get(deviceChannel).getFunctionId();
            }else {
                return false;
            }
        }else if(twoAddress.equals("Phy")){
            CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
            if (StringUtils.isNotBlank(canConfig.getUdsModels().get(deviceChannel).getRequestId())) {
                twoAddress = canConfig.getUdsModels().get(deviceChannel).getRequestId().replace("0x", "");
            }else{
                return false;
            }
        }else{
            twoAddress = secondMessageId;
        }
        boolean sendTwo = sendService(deviceChannel, twoAddress, secondByteData);
        return sendOne && sendTwo;
    }

    @Override
    public abstract boolean checkMsgByIdAndData(String messageId, String data);


    @Override
    public String checkCanFrameStatus(Integer deviceChannel,long timeOutMillis) {
        Object buffer = getAllReceiveBuffer();
        long startTime = System.currentTimeMillis();
        boolean hasNormalFrame = false;
        boolean hasErrorFrame = false;
        Object referenceData = null; // 对比帧
        long referenceTimestamp = 0;
        // 获取初始对比帧（循环前buffer的最后一帧）
        if (!isBufferAllEmpty(buffer)) {
            int bufferSize = getAllBufferSize(buffer);
            if (bufferSize > 0) {
                referenceData = readFromAllTail(buffer, 0); // 获取最后一帧
                if (referenceData != null && getChannelFromData(referenceData) == getChannel()) {
                    referenceTimestamp = getTimestamp(referenceData);
                }
            }
        }
        // 持续检测1秒钟
        while (System.currentTimeMillis() - startTime < timeOutMillis) {
            if (!isBufferAllEmpty(buffer)) {
                int bufferSize = getAllBufferSize(buffer);
                for (int i = 0; i < bufferSize; i++) {
                    Object data = readFromAllTail(buffer, i);
                    if (data != null && getChannelFromData(data) == deviceChannel) {
                        long currentTimestamp = getTimestamp(data);
                        // 只处理时间大于对比帧的帧
                        if (currentTimestamp > referenceTimestamp && getDirection(data).equalsIgnoreCase("Rx")) {
                            if (isErrorFream(data)) {
                                hasErrorFrame = true;
                            } else {
                                hasNormalFrame = true;
                            }
                        }else {
                            break;
                        }
                    }
                }
            }
            try {
                Thread.sleep(10); // 短暂休眠以避免过度占用CPU
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        // 根据收集到的帧类型返回结果
        if (hasNormalFrame && hasErrorFrame) {
            return "hasErrorAndNormal";
        } else if (hasNormalFrame) {
            return "onlyHasNormal";
        } else if (hasErrorFrame) {
            return "onlyHasError";
        } else {
            return "stop";
        }
    }

    @Override
    public String checkCanFrame(Integer deviceChannel, long timeOutMillis) {
        long startTime = System.currentTimeMillis();
        String lastResult = ""; // 默认返回值
        // 如果超时时间小于等于1秒，则只调用一次 checkCanFrameStatus
        if (timeOutMillis <= 1000) {
            return checkCanFrameStatus(deviceChannel, 1000);
        }
        // 否则循环调用 checkCanFrameStatus 直到 timeOutMillis 结束
        StringBuilder resultBuilder = new StringBuilder();
        Set<String> seenResults = new HashSet<>();
        while (System.currentTimeMillis() - startTime < timeOutMillis) {
            lastResult = checkCanFrameStatus(deviceChannel, 1000); // 每次检查1秒
            // 如果是第一次或者与之前所有结果都不同，则添加到结果中
            if (resultBuilder.length() == 0) {
                resultBuilder.append(lastResult);
                seenResults.add(lastResult);
            } else if (!seenResults.contains(lastResult)) {
                resultBuilder.append("&").append(lastResult);
                seenResults.add(lastResult);
            }
            try {
                Thread.sleep(10); // 短暂休眠以避免过度占用CPU
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        lastResult = resultBuilder.toString();
        return lastResult; // 返回最后一次调用的结果
    }

    @Override
    public long checkRecoverTime(Integer deviceChannel, long timeOutMillis) {
        Object buffer = getAllReceiveBuffer();
        long startTime = System.currentTimeMillis();
        boolean hasNormalFrame = false;
        boolean hasErrorFrame = false;
        Object referenceData = null; // 对比帧
        long referenceTimestamp = 0;
        boolean firstCheckNormal = true;// 第一次检测
        // 获取初始对比帧（循环前buffer的最后一帧）
        if (!isBufferAllEmpty(buffer)) {
            int bufferSize = getAllBufferSize(buffer);
            if (bufferSize > 0) {
                referenceData = readFromAllTail(buffer, 0); // 获取最后一帧
                if (referenceData != null && getChannelFromData(referenceData) == getChannel()) {
                    referenceTimestamp = getTimestamp(referenceData);
                }
            }
        }
        // 检查初始状态
        if (!isBufferAllEmpty(buffer)) {
            int bufferSize = getAllBufferSize(buffer);
            for (int i = 0; i < bufferSize; i++) {
                Object data = readFromAllTail(buffer, i);
                if (data != null && getChannelFromData(data) == deviceChannel && getTimestamp(data) > referenceTimestamp && getDirection(data).equalsIgnoreCase("Rx")) {
                    if (isErrorFream(data)) {
                        firstCheckNormal = false;
                        break;
                    }
                }
            }
        }
        // 如果第一次检测就是正常通信，返回0
        if (firstCheckNormal) {
            return 0;
        }
        while (System.currentTimeMillis() - startTime < timeOutMillis) {
            // 检查当前所有帧
            if (!isBufferAllEmpty(buffer)) {
                int bufferSize = getAllBufferSize(buffer);
                for (int i = 0; i < bufferSize; i++) {
                    Object data = readFromAllTail(buffer, i);
                    if (data != null && getChannelFromData(data) == deviceChannel && getDirection(data).equalsIgnoreCase("Rx")) {
                        if (isErrorFream(data)) {
                            hasErrorFrame = true;
                        } else {
                            hasNormalFrame = true;
                        }
                    }
                }
            }
            // 如果只有正常帧，没有错误帧，则认为已恢复
            if (hasNormalFrame && !hasErrorFrame) {
                return System.currentTimeMillis() - startTime;
            }
            try {
                Thread.sleep(10); // 短暂休眠以避免过度占用CPU
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        // 超时未恢复，返回-1表示未恢复
        return -1;
    }

}
