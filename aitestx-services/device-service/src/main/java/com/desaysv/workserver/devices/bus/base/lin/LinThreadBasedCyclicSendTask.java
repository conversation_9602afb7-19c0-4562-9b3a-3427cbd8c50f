package com.desaysv.workserver.devices.bus.base.lin;

import com.desaysv.workserver.devices.bus.base.CyclicSendTask;
import com.desaysv.workserver.devices.bus.base.ModifiableCyclicTask;
import com.desaysv.workserver.devices.bus.base.RestartableCyclicTask;
import com.desaysv.workserver.devices.bus.base.can.E2eError;
import com.desaysv.workserver.utils.ByteUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 基于线程的周期发送任务
 */
@Slf4j
public class LinThreadBasedCyclicSendTask extends CyclicSendTask implements ModifiableCyclicTask, RestartableCyclicTask {

    private static final int MAX_RETRY_COUNT = 5_000_000; // 最大重试次数
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private final AtomicBoolean stopped = new AtomicBoolean(false);
    private final AtomicBoolean isPaused = new AtomicBoolean(false);
    private final Condition pauseCondition;
    private final LinBus bus;
    private Thread thread;
    private Long endTime;
    private LinMessageEventListener linMessageEventListener;
    private CycleSendRunnable cycleSendRunnable;

    public LinThreadBasedCyclicSendTask(LinBus bus, LinMessage message, float period, Float duration, LinMessageEventListener linMessageEventListener) {
        super(message, period);
        this.bus = bus;
        this.linMessageEventListener = linMessageEventListener;
        pauseCondition = rwLock.writeLock().newCondition();
        setDuration(duration);
        start();
    }

    public void setDuration(Float duration) {
        if (!Objects.equals(duration, getDuration())) {
            log.info("更新报文持续时间:{}s", duration);
            super.setDuration(duration);
            if (duration != null) {
                endTime = System.currentTimeMillis() + new Float(duration * 1000).longValue();
            }
        }
    }

    @Override
    public void start() {
        if (thread == null || !thread.isAlive()) {
            stopped.set(false);
            isPaused.set(false);
            String name;
            if (getLinMessage() != null) {
                name = String.format("Cyclic Message for %s [Lin#%d-%d]", getLinMessage().getIdHex(), bus.getDeviceIndex(), getLinMessage().getChannel());
                cycleSendRunnable = new CycleSendRunnable();
                setLinMessageEventListener(linMessageEventListener);
                thread = new Thread(cycleSendRunnable, name);
            }
            thread.setDaemon(true);
            thread.start();
        }
    }

    @Setter
    @Getter
    private class CycleSendRunnable implements Runnable {

        private LinMessageEventListener linMessageEventListener;

        @Override
        public void run() {
            //FIXME：还没实现连续同一个id的事件帧报文互斥逻辑
            int counter = 0;
            int retryCount = 0;
            boolean hasLoggedError = false;
            while (!stopped.get()) {
                if (isPaused.get()) {
                    waitForResume();
                    continue;
                }
                LinMessage message = getLinMessage();
                if (linMessageEventListener != null) {
                    linMessageEventListener.onMessageSend(message);
                }
                long started = System.currentTimeMillis();
                try {
                    //连续帧发送
                    for (int i = 0; i < Math.max(1, message.getFramesPerSendNum()); i++) {
                        bus.send(message);
                    }
                    hasLoggedError = false;
                    if (message.getSendTimes() > 0) {
                        counter++;
                    }
                } catch (Throwable e) {
                    retryCount++;
                    if (!hasLoggedError) {
                        log.error(String.format("Lin通道%d报文%s发送异常", getLinMessage().getChannel(), getLinMessage().getIdHex()), e);
                        bus.sendError(getLinMessage());
                        hasLoggedError = true;
                    }
                    if (retryCount >= MAX_RETRY_COUNT) {
                        log.error("Lin发送失败超过最大重试次数{}，停止重试:{}", MAX_RETRY_COUNT, message);
                        stop(true);
                        return;
                    }
                }
                if (shouldStop(message, counter)) {
                    //发送完成
                    bus.sendFinish(getLinMessage());
                    stop(true);
                    break;
                }
                //补偿bus发送的耗时
                long delay = (long) (getPeriod() * 1000 - (System.currentTimeMillis() - started));
//                log.info("补偿bus发送的耗时:{}", delay);
                if (delay > 0) {
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException e) {
                        log.warn("周期发送任务被中断", e);
                        Thread.currentThread().interrupt();
                        return;
                    }
                } else {
                    Thread.yield();  // 让出CPU时间片
                }
            }
        }

    }

    private boolean shouldStop(LinMessage message, int counter) {
        if (message.getSendTimes() > 0 && counter == message.getSendTimes()) {
            //事件帧发送
            log.info("Lin报文{}的{}次事件帧发送完成", message.getIdHex(), message.getSendTimes());
            return true;
        } else if (endTime != null && System.currentTimeMillis() >= endTime) {
            log.info("Lin报文{}整个周期发送完成", message.getIdHex());
            return true;
        }
        return false;
    }


    private void waitForResume() {
        rwLock.writeLock().lock();
        try {
            while (isPaused.get()) {
                try {
                    pauseCondition.await();
                } catch (InterruptedException e) {
                    log.warn("等待恢复时被中断", e);
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    protected void _stopTask() {
//        if (stopped) {
//            throw new CanError("CAN报文已经停止");
//        }
        stopped.set(true);
        //thread.interrupt();
    }

    @Override
    public void modifyData(byte[] newData) {
        rwLock.writeLock().lock();
        try {
            byte[] oldData = getLinMessage().getData();
//        byte[] newData = message.getData();
            if (!Arrays.equals(oldData, newData)) {
                log.info("Lin报文{}数据更新为:{}", getLinMessage().getIdHex(), ByteUtils.byteArrayToHexString(oldData));
                System.arraycopy(newData, 0, oldData, 0, oldData.length);
                getLinMessage().setData(oldData);
            } else {
                log.info("Lin报文数据跟上次一致:{}", ByteUtils.byteArrayToHexString(oldData));
            }
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void modifyE2eError(E2eError e2eError) {

    }

    @Override
    public void modifyData(LinMessage message) {
        rwLock.writeLock().lock();
        try {
            byte[] oldData = getLinMessage().getData();
//        byte[] newData = message.getData();
            if (!Arrays.equals(oldData, message.getData())) {
                log.info("更新Lin报文{}数据:{}", getLinMessage().getIdHex(), ByteUtils.byteArrayToHexString(oldData));
                System.arraycopy(message.getData(), 0, oldData, 0, oldData.length);
                getLinMessage().setData(oldData);
            } else {
                log.info("Lin报文数据跟上次一致");
            }
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void modifyPeriod(float period) {
        rwLock.writeLock().lock();
        try {
            setPeriod(period);
        } finally {
            rwLock.writeLock().unlock();
        }
    }


    @Override
    public void modifyDlc(int length) {
        rwLock.writeLock().lock();
        try {
            log.info("更新Lin报文{}Length:{}", getLinMessage().getIdHex(), length);
            ByteUtils.adjustByteArray(getLinMessage().getData(), length);
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void pause() {
        log.info("通道{}暂停报文:{}", getLinMessage().getChannel(), getLinMessage().getIdHex());
        isPaused.set(true);
    }

    @Override
    public void resume() {
        log.info("通道{}正在恢复报文:{}", getLinMessage().getChannel(), getLinMessage().getIdHex());
        rwLock.writeLock().lock();
        try {
            isPaused.set(false);
            pauseCondition.signalAll(); // 唤醒等待的线程
        } finally {
            rwLock.writeLock().unlock();
        }
        log.info("通道{}完成恢复报文:{}", getLinMessage().getChannel(), getLinMessage().getIdHex());
    }

    public LinMessageEventListener getLinMessageEventListener() {
        return cycleSendRunnable.getLinMessageEventListener();
    }

    public void setLinMessageEventListener(LinMessageEventListener linMessageEventListener) {
        this.linMessageEventListener = linMessageEventListener;
        cycleSendRunnable.setLinMessageEventListener(linMessageEventListener);
    }

    public static void main(String[] args) {
        byte[] data = new byte[]{1, 2, 3, 4};
        int dlc = 2;
        byte[] bytes = ByteUtils.adjustByteArray(data, dlc);
        System.out.println(Arrays.toString(bytes));
    }
}
