package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.FilterCanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.model.BusData;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ZCAN_USBCANFD_200U底层接口
 */
@Slf4j
public class ZlgCanFd200U extends ZlgCan {

    private final ZlgCanAbstractionLayer can;

    public ZlgCanFd200U(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        can = new ZlgCanAbstractionLayer();
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.ZLG_USBCANFD_200U;
    }

    @Override
    public List<Integer> fetchRunningCanMessage(Integer deviceChannel) {
        List<Integer> result = new ArrayList<>();
        // 获取父类方法的结果
        List<Integer> superResult = super.fetchRunningCanMessage(deviceChannel);
        if (superResult != null) {
            result.addAll(superResult);
        }
        // 如果不是软件发送模式，也获取硬件发送的消息ID
        Map<Integer, Object> channelMap = ZlgApi.getMap().get(deviceChannel);
        if (channelMap != null) {
            for (Map.Entry<Integer, Object> entry : channelMap.entrySet()) {
                if (entry.getValue() instanceof ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) {
                    ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) entry.getValue();
                    if (obj.enable == 1) {
                        result.add(entry.getKey());
                    }
                } else if (entry.getValue() instanceof ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) {
                    ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) entry.getValue();
                    if (obj.enable == 1) {
                        result.add(entry.getKey());
                    }
                }
            }
        }
        return result;
    }

    @Override
    public CyclicTask sendPeriodic(CanMessage message, float period, Float duration) {
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        boolean isHardwareSend = false;
        // 检查全局发送模式是否为硬件发送且message没有开启E2E
        if (deviceConfig.getGlobalCanModel().isZlgAllHardware() && !message.isE2eEnabled()) {
            isHardwareSend = true;
        }
        // 检查报文是否开启硬件发送且没有开启E2E
        else if ("硬件发送".equals(message.getSendMode()) && !message.isE2eEnabled()) {
            isHardwareSend = true;
        }
        // 获取当前通道已存在的周期任务
        Integer deviceChannel = message.getChannel();
        Integer messageId = message.getArbitrationId();
        Map<Integer, Object> channelMap = ZlgApi.getMap().get(deviceChannel);
        boolean isExistingHardwareMsg = false;
        boolean isExistingSoftwareMsg = false;
        // 检查硬件中是否存在该报文
        if (channelMap != null && channelMap.containsKey(messageId)) {
            Object msgObj = channelMap.get(messageId);
            if (msgObj instanceof ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) {
                ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) msgObj;
                if (obj.enable == 1) {
                    isExistingHardwareMsg = true;
                }
            } else if (msgObj instanceof ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) {
                ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) msgObj;
                if (obj.enable == 1) {
                    isExistingHardwareMsg = true;
                }
            }
        }
        List<Integer> superResult = super.fetchRunningCanMessage(deviceChannel);
        if(superResult.contains(messageId)){
            isExistingSoftwareMsg = true;
        }
        // 如果存在不同类型的发送，先停止原来的发送
        if ((isHardwareSend && isExistingSoftwareMsg) || (!isHardwareSend && isExistingHardwareMsg)) {
            stopCanMessage(deviceChannel, messageId);
        }
        if (isHardwareSend) {
            can.sendPeriod(getDeviceHandle(), message);
        } else {
            return super.sendPeriodic(message, period, duration);
        }
        return null;
    }

    @Override
    public CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId) {
        Map<Integer, Object> channelMap = ZlgApi.getMap().get(deviceChannel);
        boolean isHardwareMsg = false;
        // 检查硬件中是否存在该报文
        if (channelMap != null && channelMap.containsKey(messageId)) {
            Object msgObj = channelMap.get(messageId);
            if (msgObj instanceof ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) {
                ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) msgObj;
                if (obj.enable == 1) {
                    isHardwareMsg = true;
                }
            } else if (msgObj instanceof ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) {
                ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) msgObj;
                if (obj.enable == 1) {
                    isHardwareMsg = true;
                }
            }
        }
        if (isHardwareMsg) {
            // 硬件中有此报文，停掉硬件报文
            ZlgApi.stopCanPeriodMessage(getDeviceHandle(), deviceChannel, messageId);
        } else {
            // 硬件中没有此报文，走父类停止逻辑
            super.stopCanMessage(deviceChannel, messageId);
        }
        log.info("{}停止CAN通道{}报文:0x{}", getDeviceName(), deviceChannel, String.format("%X", messageId));
        return null;
    }

    @Override
    public boolean stopAllCanMessage(Integer deviceChannel) {
        // 停止软件发送的所有周期报文
        super.stopAllCanMessage(deviceChannel);
        // 停止硬件发送的所有周期报文
        Map<Integer, Object> channelMap = ZlgApi.getMap().get(deviceChannel);
        if (channelMap != null) {
            for (Map.Entry<Integer, Object> entry : channelMap.entrySet()) {
                ZlgApi.stopCanPeriodMessage(getDeviceHandle(), deviceChannel, entry.getKey());
            }
        }
        log.info("{}停止通道{}所有周期报文", getDeviceName(), deviceChannel);
        return true;
    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {
        boolean isSendOk;
        if (timeout != null) {
            //阻塞发送
            isSendOk = can.blockingSend(message, timeout);
        } else {
            //正常发送
            isSendOk = can.send(getChannelHandle(message.getChannel()), message);
        }

        if (!isSendOk) {
            throw new BusError(String.format("could not send message:%s", message));
        }

    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }


    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        BusData busData = getZlg200UBusDataByJsonConfig(deviceChannel);
        return sendAllIGModuleMessages(busData, deviceChannel, command);
    }


    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError {
        BusData busData = getZlg200UBusDataByJsonConfig(deviceChannel);
        return sendSingleIGModuleMessages(busData, deviceChannel, igTabName, command);
    }

    private BusData getZlg200UBusDataByJsonConfig(Integer deviceChannel) {
        String filePath = String.format("D:\\FlyTest\\data\\client\\projects\\%s\\config\\dbc\\%s\\channel%d\\data.json", projectName, getDeviceName(), deviceChannel);
        return importBusDataFile(filePath);
    }
}
