package com.desaysv.workserver.common.constant;

public class CanConstants {
    // 实时保存模式常量
    public static final Integer DEFAULT_FILE_SIZE_MB = 300; // 默认文件大小限制(MB)
    public static final Integer DEFAULT_RECORD_COUNT = 200000; // 默认记录条数限制

    // E2E通信协议类型常量
    public static final String E2E_TYPE_GEELY_L946 = "GeelyL946";
    public static final String E2E_TYPE_CHERY_E01 = "CheryE01";
    public static final String E2E_TYPE_XIAO_MI = "Xiaomi";
    public static final String E2E_TYPE_GAC = "GAC";
    public static final String E2E_TYPE_OTHER = "Other";
}