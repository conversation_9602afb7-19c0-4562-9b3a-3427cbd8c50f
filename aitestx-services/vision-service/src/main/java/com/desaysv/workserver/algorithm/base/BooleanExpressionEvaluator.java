package com.desaysv.workserver.algorithm.base;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BooleanExpressionEvaluator {

    private final Map<String, Supplier<Boolean>> functionMap;

    public BooleanExpressionEvaluator(Map<String, Supplier<Boolean>> functionMap) {
        this.functionMap = functionMap;
    }

    public boolean evaluate(String expression) {
        List<String> tokens = tokenize(expression);
        return evaluateOR(tokens, 0, tokens.size());
    }

    private List<String> tokenize(String expression) {
        List<String> tokens = new ArrayList<>();
        StringBuilder currentToken = new StringBuilder();

        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);
            if (c == '|' || c == '&') {
                if (currentToken.length() > 0) {
                    tokens.add(currentToken.toString());
                    currentToken = new StringBuilder();
                }
                tokens.add(String.valueOf(c));
            } else if (c == '!') {
                if (currentToken.length() > 0) {
                    tokens.add(currentToken.toString());
                    currentToken = new StringBuilder();
                }
                currentToken.append(c);
            } else {
                currentToken.append(c);
            }
        }

        if (currentToken.length() > 0) {
            tokens.add(currentToken.toString());
        }

        return tokens;
    }

    private boolean evaluateOR(List<String> tokens, int start, int end) {
        for (int i = start; i < end; i++) {
            if (tokens.get(i).equals("|")) {
                return evaluateOR(tokens, start, i) || evaluateOR(tokens, i + 1, end);
            }
        }
        return evaluateAND(tokens, start, end);
    }

    private boolean evaluateAND(List<String> tokens, int start, int end) {
        for (int i = start; i < end; i++) {
            if (tokens.get(i).equals("&")) {
                return evaluateAND(tokens, start, i) && evaluateAND(tokens, i + 1, end);
            }
        }
        return evaluateNOT(tokens, start, end);
    }

    private boolean evaluateNOT(List<String> tokens, int start, int end) {
        String token = tokens.get(start);
        if (token.startsWith("!")) {
            return !evaluateFunction(token.substring(1));
        }
        return evaluateFunction(token);
    }

    private boolean evaluateFunction(String functionName) {
        Supplier<Boolean> function = functionMap.get(functionName);
        if (function == null) {
            throw new IllegalArgumentException("Unknown function: " + functionName);
        }
        return function.get();
    }

    // 更新自动填充 functionMap 的方法以支持中文
    public static Map<String, Supplier<Boolean>> autoPopulateFunctionMap(String expression, Function<String, Supplier<Boolean>> supplierFactory) {
        Map<String, Supplier<Boolean>> functionMap = new HashMap<>();
        Set<String> functionNames = extractFunctionNames(expression);
        for (String functionName : functionNames) {
            functionMap.put(functionName, supplierFactory.apply(functionName));
        }
        return functionMap;
    }

    // 更新辅助方法：从表达式中提取函数名，支持中文
    private static Set<String> extractFunctionNames(String expression) {
        Set<String> functionNames = new HashSet<>();
        // 正则表达式支持中文字符
        Pattern pattern = Pattern.compile("[\\p{IsHan}\\w]+");
        Matcher matcher = pattern.matcher(expression);
        while (matcher.find()) {
            functionNames.add(matcher.group());
        }
        return functionNames;
    }

    // Usage example
    public static void main(String[] args) {
        String expression = "ak";

        // 自动生成 functionMap，使用自定义的 supplierFactory
        Map<String, Supplier<Boolean>> functionMap = autoPopulateFunctionMap(expression,
                functionName -> () -> {
                    System.out.println("执行: " + functionName);
                    return true;  // 默认返回 true
                }
        );

        // 可以在这里修改 functionMap，如果需要的话
        // 例如：functionMap.put("屏幕", () -> { System.out.println("自定义 屏幕"); return false; });

        BooleanExpressionEvaluator evaluator = new BooleanExpressionEvaluator(functionMap);

        boolean result = evaluator.evaluate(expression);
        System.out.println("Result: " + result);
    }
}