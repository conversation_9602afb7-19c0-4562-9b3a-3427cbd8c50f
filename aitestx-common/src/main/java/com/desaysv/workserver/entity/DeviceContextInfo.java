package com.desaysv.workserver.entity;

import lombok.Data;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/7/10 15:24
 * @Version: 1.0
 * @Desc : 设备信息传递类
 */

@Data
public class DeviceContextInfo {

    private static final DeviceContextInfo INSTANCE = new DeviceContextInfo();

    private Map<String, String> messageDataMap = new ConcurrentHashMap<>();

    private float stopVoltage;

    private float normalLowVoltage;

    private float normalHighVoltage;

    private DeviceContextInfo() {
        // 私有构造函数，防止外部实例化
    }

    public static DeviceContextInfo getInstance() {
        return INSTANCE;
    }

}
