package com.desaysv.workserver.utils;

import org.apache.commons.codec.binary.Hex;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.IntBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Locale;

public class ByteUtils {

    public static boolean isEmpty(byte[] array) {
        return array == null || array.length == 0;
    }

    public static byte[] adjustByteArray(byte[] originalArray, int targetLength) {
        byte[] newArray = new byte[targetLength];

        if (originalArray.length < targetLength) {
            // 如果原数组长度小于指定长度，用0填充
            System.arraycopy(originalArray, 0, newArray, 0, originalArray.length);
            for (int i = originalArray.length; i < targetLength; i++) {
                newArray[i] = 0;
            }
        } else {
            // 如果原数组长度大于指定长度，截断
            System.arraycopy(originalArray, 0, newArray, 0, targetLength);
        }

        return newArray;
    }

    public static byte uniteBytes(byte src0, byte src1) {
        byte _b0 = Byte.decode("0x" + new String(new byte[]{src0}));
        _b0 = (byte) (_b0 << 4);
        byte _b1 = Byte.decode("0x" + new String(new byte[]{src1}));
        return (byte) (_b0 ^ _b1);
    }

    public static int[] hexStringToIntArray(String hex) {
        if (hex == null) {
            return new int[]{};
        }
        hex = hex.replaceAll("\\s+", "");
        // 奇数位补0
        if (hex.length() % 2 != 0) {
            hex = hex.substring(0, hex.length() - 1) + "0" + hex.substring(hex.length() - 1);
        }
        int length = hex.length();
        IntBuffer buffer = IntBuffer.allocate(length / 2);
        for (int i = 0; i < length; i++) {
            String hexStr = hex.charAt(i) + "";
            i++;
            hexStr += hex.charAt(i);
            int b = Integer.parseInt(hexStr, 16);
            buffer.put(b);
        }
        return buffer.array();
    }

    public static byte[] hexStringToByteArray(String hex) {
        // 算法参考DatatypeConverter.parseHexBinary()
        if (hex == null) {
            return new byte[]{};
        }
        hex = hex.replaceAll("\\s+", "");
        // 奇数位补0
        if (hex.length() % 2 != 0) {
            hex = hex.substring(0, hex.length() - 1) + "0" + hex.substring(hex.length() - 1);
        }

        int length = hex.length();
        ByteBuffer buffer = ByteBuffer.allocate(length / 2);
        for (int i = 0; i < length; i++) {
            String hexStr = hex.charAt(i) + "";
            i++;
            hexStr += hex.charAt(i);
            byte b = (byte) Integer.parseInt(hexStr, 16);
            buffer.put(b);
        }
        return buffer.array();
    }


    /**
     * byte[]转十六进制字符串
     */
    public static String byteArrayToHexString(byte[] array) {
        if (array == null) {
            return "";
        }
        StringBuilder buffer = new StringBuilder();
        for (byte b : array) {
            buffer.append(byteToHex(b)).append(" ");
        }
        return buffer.toString().trim();
    }

    public static String intArrayToHexString(int[] array) {
        if (array == null) {
            return "";
        }
        StringBuilder buffer = new StringBuilder();
        for (int i : array) {
            buffer.append(intToHex(i & 0xFF)).append(" ");
        }
        return buffer.toString().trim();
    }

    public static String intToHex(int i) {
        String hex = Integer.toHexString(i);
        if (hex.length() % 2 == 1) {
            hex = '0' + hex;
        }
        return beautifyHex(hex.toUpperCase(Locale.getDefault()));
    }

    public static int hexToInt(String hexStr) {
        return Integer.parseInt(hexStr.replace("0x", ""), 16);
    }

    private static String beautifyHex(String input) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < input.length(); i += 2) {
            if (i > 0) {
                stringBuilder.append(" "); // 在每两个字符之间插入一个空格
            }
            stringBuilder.append(input, i, Math.min(i + 2, input.length()));
        }
        return stringBuilder.toString();
    }

    /**
     * byte转十六进制字符
     */
    public static String byteToHex(byte b) {
        String hex = Integer.toHexString(b & 0xFF);
        if (hex.length() == 1) {
            hex = '0' + hex;
        }
        return hex.toUpperCase(Locale.getDefault());
    }

//    public static void main(String[] args) {
//        System.out.println(Hex.encodeHexString(hexStringToByteArray("1112")));
//    }

    public static int additionHex(String hexStr1, String hexStr2) {
        int hexStrNum1 = Integer.parseInt(hexStr1.substring(2), 16);
        int hexStrNum2 = Integer.parseInt(hexStr2.substring(2), 16);
        int sum = hexStrNum1 + hexStrNum2;
        //System.out.println("加法结果（10进制）: " + sum);
        //System.out.println("加法结果（16进制）: 0x" + Integer.toHexString(sum));
        return sum;
    }

    public static int subtractionHex(String hexStr1, String hexStr2) {
        int hexStrNum1 = Integer.parseInt(hexStr1.substring(2), 16);
        int hexStrNum2 = Integer.parseInt(hexStr2.substring(2), 16);
        int diff = hexStrNum1 - hexStrNum2;
//        System.out.println("减法结果（10进制）: " + diff);
//        System.out.println("减法结果（16进制）: 0x" + Integer.toHexString(diff));
        return diff;
    }

    public static String bytesToHexString(byte[] byteArray) {
        StringBuilder sb = new StringBuilder();
        for (byte b : byteArray) {
            // 将字节转换为无符号整数（0-255），然后转换为字符串
            sb.append(String.format("%02X", b & 0xFF));
        }
        return sb.toString();
    }

    public static byte[] bytesToHexByteArray(byte[] decimalArray) {
        byte[] hexArray = new byte[decimalArray.length];
        // 直接将十进制数组赋值给十六进制数组
        System.arraycopy(decimalArray, 0, hexArray, 0, decimalArray.length);
        return hexArray;
    }

    // 将字节数组转换为十六进制字符串
    private static String byteArrToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }

    //计算校验和
    public static byte calculateChecksumValue(byte[] bytes) {
        // 求和并取低8位
        int sum = 0;
        for (byte b : bytes) {
            sum += b & 0xFF; // 确保加的是无符号值
        }
        byte result = (byte) (sum & 0xFF); // 取低8位
        return result;
    }

    public static byte[] hexStringToByteArr(String dataString) {
        String s = dataString.replaceAll("\\s+", "");
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    public static byte[] StringToHexByteArr(String command) {
        if (command == null || command.isEmpty()) {
            return new byte[0];
        }
        // 去空格并处理奇数长度（补前导零）
        String hexStr = command.replaceAll("\\s+", "");
        if (hexStr.length() % 2 != 0) {
            hexStr = "0" + hexStr; // 补零保证每两个字符表示一个字节
        }
        // 逐字符解析为字节数组
        byte[] bytes = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length(); i += 2) {
            int highNibble = Character.digit(hexStr.charAt(i), 16);   // 高位字符转数值
            int lowNibble = Character.digit(hexStr.charAt(i + 1), 16); // 低位字符转数值
            bytes[i / 2] = (byte) ((highNibble << 4) | lowNibble);    // 组合为字节
        }
        return bytes;
    }

    /**
     * 将十进制整数转换为6位16进制字符串，不足部分用前导零填充。
     *
     * @param value 十进制整数
     * @return 格式化后的6位16进制字符串
     */
    public static String convertIntToSixHexBytes(int value) {
        // 确保值在可表示的范围内
        if (value < 0 || value > 0xFFFFFF) {
            throw new IllegalArgumentException("Value out of range for 24-bit representation");
        }
        // 使用String.format来格式化为6位16进制字符串
        return String.format("%06X", value);
    }

    public static String convertIntToFourHexBytes(int value) {
        // 确保值在可表示的范围内
        if (value < 0 || value > 0xFFFFFF) {
            throw new IllegalArgumentException("Value out of range for 24-bit representation");
        }
        // 使用String.format来格式化为6位16进制字符串
        return String.format("%04X", value);
    }


    public static String convertIntToTwoHexBytes(int value) {
        // 确保值在可表示的范围内
        if (value < 0 || value > 0xFFFFFF) {
            throw new IllegalArgumentException("Value out of range for 24-bit representation");
        }
        // 使用String.format来格式化为6位16进制字符串
        return String.format("%02X", value);
    }

    /**
     * 将十进制整数转换为小端序十六进制字节数组
     *
     * @param number      输入数值（支持 short 范围：-32768 ~ 32767）
     * @param targetBytes 目标字节长度（如2字节、4字节等）
     * @return 小端序字节数组
     * @throws IllegalArgumentException 数值超过目标字节范围时抛出异常
     */
    public static byte[] decimalToLittleEndianBytes(int number, int targetBytes) {
        // 数值范围校验（以2字节为例）
        if (targetBytes == 2 && (number < Short.MIN_VALUE || number > Short.MAX_VALUE)) {
            throw new IllegalArgumentException("数值超出2字节范围(-32768~32767)");
        }

        // 方法1：使用ByteBuffer自动处理小端序（推荐）
        ByteBuffer buffer = ByteBuffer.allocate(targetBytes);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        switch (targetBytes) {
            case 2:
                buffer.putShort((short) number);
                break;
            case 4:
                buffer.putInt(number);
                break;
            default:
                throw new IllegalArgumentException("暂不支持该字节长度");
        }
        return buffer.array();
    }

    /**
     * 将字节数组转换为字符串，并移除指定填充字符
     *
     * @param bytes   原始字节数组
     * @param padChar 填充字符的ASCII码（如0x20表示空格）
     * @param charset 字符集
     * @return 去除填充字符后的字符串
     */
    public static String bytesToStringWithPadRemoval(byte[] bytes, byte padChar, java.nio.charset.Charset charset) {
        int endIndex = bytes.length;
        while (endIndex > 0 && bytes[endIndex - 1] == padChar) {
            endIndex--;
        }
        return new String(bytes, 0, endIndex, charset);
    }

    /**
     * 将字符串转换为固定长度的字节数组（自动填充或截断）
     *
     * @param input        原始字符串
     * @param targetLength 目标字节数组长度
     * @param charset      字符编码（默认ASCII）
     * @param padByte      填充字节（默认空格0x20）
     * @return 固定长度的字节数组
     */
    public static byte[] toPaddedByteArray(String input, int targetLength,
                                           Charset charset, byte padByte) {
        // 参数校验
        if (targetLength <= 0) throw new IllegalArgumentException("目标长度需大于0");

        // 获取原始字节数组
        byte[] originalBytes = input.getBytes(charset != null ? charset : StandardCharsets.US_ASCII);

        // 创建目标数组并填充
        byte[] paddedBytes = new byte[targetLength];
        Arrays.fill(paddedBytes, padByte);
        System.arraycopy(originalBytes, 0, paddedBytes, 0,
                Math.min(originalBytes.length, targetLength));

        return paddedBytes;
    }

    /**
     * 将日期字符串转换为固定长度的字节数组
     *
     * @param dateStr    输入字符串（格式：yy_mm_dd）
     * @param byteLength 目标字节长度（需为3）
     * @return 3字节数组（年、月、日）
     * @throws IllegalArgumentException 输入格式错误或数值越界
     */
    public static byte[] dateToBytes(String dateStr, int byteLength) {
        // 参数校验
        if (byteLength != 3) {
            throw new IllegalArgumentException("字节长度必须为3");
        }
        // 1. 解析字符串为年月日
        String[] parts = splitDate(dateStr);
        if (parts.length != 3) {
            throw new IllegalArgumentException("输入格式应为yymmdd");
        }
        // 2. 数值转换
        int year = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]);
        int day = Integer.parseInt(parts[2]);
        // 3. 数值范围校验
        if (year < 0 || year > 255 || month < 1 || month > 12 || day < 1 || day > 31) {
            throw new IllegalArgumentException("数值越界: 年(0-255),月(1-12),日(1-31)");
        }
        // 4. 转为字节数组
        return new byte[]{
                (byte) year,
                (byte) month,
                (byte) day
        };
    }

    public static String[] splitDate(String input) {
        // 验证输入规则：4-6位纯数字
        if (!input.matches("\\d{4,6}")) {
            throw new IllegalArgumentException("输入必须为4-6位数字");
        }
        int len = input.length();
        String year = input.substring(0, 2);  // 年始终取前两位
        // 根据长度动态处理月、日
        if (len == 4) {
            return new String[]{
                    year,
                    formatTwoDigits(input.substring(2, 3)), // 月（1位补零）
                    formatTwoDigits(input.substring(3, 4))  // 日（1位补零）
            };
        } else if (len == 5) {
            return new String[]{
                    year,
                    formatTwoDigits(input.substring(2, 3)), // 月（1位补零）
                    input.substring(3, 5)                   // 日（直接取后两位）
            };
        } else { // len == 6
            return new String[]{
                    year,
                    input.substring(2, 4),  // 月（直接取中间两位）
                    input.substring(4, 6)   // 日（直接取后两位）
            };
        }
    }

    // 将1位数字补零为2位（如3→03）
    private static String formatTwoDigits(String singleDigit) {
        return String.format("%02d", Integer.parseInt(singleDigit));
    }

    public static boolean compare(byte[] bytes, String hexString) {
        // 1. 处理输入的字符串：移除前缀并转为大写
        String processedHex = hexString.replaceAll("^0x", "").toUpperCase();
        // 2. 将byte数组按小端序（逆序）转换为十六进制字符串
        StringBuilder hexBuilder = new StringBuilder();
        for (int i = bytes.length - 1; i >= 0; i--) {
            hexBuilder.append(String.format("%02X", bytes[i] & 0xFF));
        }
        String byteArrayHex = hexBuilder.toString();
        // 3. 比较结果
        return byteArrayHex.equals(processedHex);
    }

    public static String addChecksum(String hexString, java.util.function.Function<byte[], Byte> checksumFunc) {
        byte[] data = hexStringToByteArray(hexString);
        byte checksum = checksumFunc.apply(data);
        return hexString + " " + String.format("%02X", checksum & 0xFF);
    }

    public static byte calculateAdd8(byte[] data) {
        int sum = 0;
        for (byte b : data) {
            sum += b & 0xFF;
        }
        return (byte) (sum & 0xFF);
    }

    public static byte calculateXor8(byte[] data) {
        int xor = 0;
        for (byte b : data) {
            xor ^= b & 0xFF;
        }
        return (byte) (xor & 0xFF);
    }

    public static byte calculateCrc8(byte[] data) {
        int crc = 0;
        for (byte b : data) {
            crc ^= (b & 0xFF);
            for (int i = 0; i < 8; i++) {
                if ((crc & 0x80) != 0) {
                    crc = (crc << 1) ^ 0x07;
                } else {
                    crc <<= 1;
                }
            }
        }
        return (byte) (crc & 0xFF);
    }

    public static void main(String[] args) {
        byte[] bytes = dateToBytes("25327", 3);
        System.out.println("byte--" + Hex.encodeHexString(bytes));
        byte[] testBytes = {(byte) 0x84}; // 注意第二个字节是十六进制的0x81（十进制-127）
        String expected = "0x84";
        boolean result1 = compare(testBytes, expected);
        System.out.println("匹配结果: " + result1); // 输出: true

        byte[] decimalArray = {48, 70, 0, 0, 0, 0, 0, 0};
        byte[] hexArray = bytesToHexByteArray(decimalArray);

        // 直接将十进制数组赋值给十六进制数组
//        System.arraycopy(decimalArray, 0, hexArray, 0, decimalArray.length);

        // 输出结果
        System.out.print("{");
        for (int i = 0; i < hexArray.length; i++) {
            if (i > 0) {
                System.out.print(", ");
            }
            System.out.printf("%02X", hexArray[i]);
        }
        System.out.println("}");

        String s = bytesToHexString(decimalArray);
        System.out.println("s---" + s);
        byte[] byteArray = {
                0x4C, 0x41, 0x41, 0x2D, 0x37, 0x30, 0x30, 0x38,
                0x30, 0x31, 0x30, 0x32, 0x20, 0x20, 0x20
        };
        String result = ByteUtils.bytesToStringWithPadRemoval(byteArray, (byte) 0x20, StandardCharsets.US_ASCII);
        System.out.println(result); // 输出: LAA-70080102
    }

}