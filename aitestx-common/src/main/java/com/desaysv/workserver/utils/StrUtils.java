package com.desaysv.workserver.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.JexlBuilder;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlExpression;
import org.apache.commons.jexl3.MapContext;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.apache.commons.codec.digest.DigestUtils.md5Hex;

/**
 * 字符串工具类
 */
@Slf4j
public class StrUtils {

    /**
     * Extracts all hexadecimal IDs (starting with '0x') from the given input string.
     *
     * @param input The input string to search for hexadecimal IDs.
     * @return A list of extracted hexadecimal IDs. Returns an empty list if no matches are found.
     */
    public static String extractHexId(String input) {
        List<String> hexIds = new ArrayList<>();
        String regex = "0x[0-9A-Fa-f]+";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            hexIds.add(matcher.group());
        }

        return hexIds.isEmpty() ? null : hexIds.get(0);
    }

    public static double getDiff(CharSequence s, CharSequence t) {
        if (s == null || t == null) {
            return 0;
        }
        int distance = StringUtils.getLevenshteinDistance(s, t);
        return 1 - (double) distance / Math.max(s.length(), t.length());
    }

    public static boolean isEmpty(String cs) {
        if (cs != null) {
            cs = cs.trim();
        }
        return StringUtils.isEmpty(cs);
    }

    public static String bytesToString(byte[] bytes) {
        String[] strSerialNum = new String(bytes).split("\0");
        if(strSerialNum.length != 0){
            return strSerialNum[0];
        }
        return "";
    }

    public static String trimStart(String inStr, String prefix) {
        if (inStr.startsWith(prefix)) {
            return inStr.substring(prefix.length());
        }
        return inStr;
    }

    public static String trimEnd(String inStr, String suffix) {
        if (inStr.endsWith(suffix)) {
            return inStr.substring(0, inStr.length() - suffix.length());
        }
        return inStr;
    }

    public static String removeSuffix(String input, String regex) {
        if (!regex.endsWith("$")) {
            regex += "$";
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return input.substring(0, matcher.start());
        } else {
            return input;
        }
    }

    public static String removePrefix(String input, String regex) {
        if (!regex.startsWith("^")) {
            regex = "^" + regex;
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return input.substring(matcher.start() + 1);
        } else {
            return input;
        }
    }

    public static boolean judgeExpressResult(String booleanExpress) {
        JexlBuilder jexlBuilder = new JexlBuilder();
        JexlEngine jexl = jexlBuilder.create();
        JexlExpression jexlExpression = jexl.createExpression(booleanExpress);
        MapContext jexlContext = new MapContext();
        return (boolean) jexlExpression.evaluate(jexlContext);
    }


    public static String getHexStringWithBlank(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    public static String getHexStringWithBlank(List<byte[]> loopDatas) {
        // 将 List<byte[]> 转换为格式化的十六进制字符串
        return loopDatas.stream()
                .map(StrUtils::getHexStringWithBlank)
                .collect(Collectors.joining(", "));
    }

    public static String getHexString(byte[] bytes) {
        return new BigInteger(1, bytes).toString(16);
    }

    public static String cleanRequestString(String string) {
        return trimBothEndsQuotation(string)
                .replace("\\r\\n", System.lineSeparator())
                .replace("\\r", "\r")
                .replace("\\n", "\n");
    }

    public static String trimBothEndsQuotation(String string) {
        return string.substring(1, string.length() - 1);
    }

    public static String firstCapitalString(String string) {
        string = string.substring(0, 1).toUpperCase() + string.substring(1);
        return string;
    }


    public static String encodeToBase64(String message) {
        return Base64.getEncoder().encodeToString(message.getBytes(StandardCharsets.UTF_8));
    }

    public static String decodeToBase64(String message) {
        return new String(Base64.getDecoder().decode(message), StandardCharsets.UTF_8);
    }

    public static String replaceSpecialString(String message) {
//        return message.replaceAll("\\\\", "/");
        return message;
    }

    public static String simpleDateFileName() {
        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return simpleDateFormat.format(date);
    }

    public static String getMd5String(String input) {
        return getMd5String(input, false);
    }

    public static String getMd5String(String input, boolean isCapitalization) {
        try {
//            拿到一个MD5转换器（如果想要SHA1参数换成”SHA1”）
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
//            输入的字符串转换成字节数组
            byte[] inputByteArray = input.getBytes();
//            inputByteArray是输入字符串转换得到的字节数组
            messageDigest.update(inputByteArray);
//            转换并返回结果，也是字节数组，包含16个元素
            byte[] resultByteArray = messageDigest.digest();
//            字符数组转换成字符串返回
            String md5String = byteArrayToHex(resultByteArray);
            return isCapitalization ? md5String.toUpperCase() : md5String.toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    // 将字节数组换成成16进制的字符串
    private static String byteArrayToHex(byte[] byteArray) {
        // 首先初始化一个字符数组，用来存放每个16进制字符
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        // new一个字符数组，这个就是用来组成结果字符串的（解释一下：一个byte是八位二进制，也就是2位十六进制字符（2的8次方等于16的2次方））
        char[] resultCharArray = new char[byteArray.length * 2];
        // 遍历字节数组，通过位运算（位运算效率高），转换成字符放到字符数组中去
        int index = 0;
        for (byte b : byteArray) {
            resultCharArray[index++] = hexDigits[b >>> 4 & 0xf];
            resultCharArray[index++] = hexDigits[b & 0xf];
        }
        // 字符数组组合成字符串返回
        return new String(resultCharArray);
    }

    //生成“盐”和加盐后的MD5码，并将盐混入到MD5码中
    public static String generate(String password) {
        //生成一个16位的随机数，也就是所谓的“盐”
        Random r = new Random();
        StringBuilder sb = new StringBuilder(16);
        sb.append(r.nextInt(99999999)).append(r.nextInt(99999999));
        int len = sb.length();
        if (len < 16) {
            for (int i = 0; i < 16 - len; i++) {
                sb.append("0");
            }
        }
        String salt = sb.toString();
        //将“盐”加到明文中，并生成新的MD5码
        password = md5Hex(password + salt);
        //将“盐”混到新生成的MD5码中，之所以这样做是为了后期更方便的校验明文和秘文，也可以不用这么做，不过要将“盐”单独存下来，不推荐这种方式
        char[] cs = new char[48];
        for (int i = 0; i < 48; i += 3) {
            cs[i] = password.charAt(i / 3 * 2);
            char c = salt.charAt(i / 3);
            cs[i + 1] = c;
            cs[i + 2] = password.charAt(i / 3 * 2 + 1);
        }
        return new String(cs);
    }

    //验证明文和加盐后的MD5码是否匹配
    public static boolean verify(String password, String md5) {
        //先从MD5码中取出之前加的“盐”和加“盐”后生成的MD5码
        char[] cs1 = new char[32];
        char[] cs2 = new char[16];
        for (int i = 0; i < 48; i += 3) {
            cs1[i / 3 * 2] = md5.charAt(i);
            cs1[i / 3 * 2 + 1] = md5.charAt(i + 2);
            cs2[i / 3] = md5.charAt(i + 1);
        }
        String salt = new String(cs2);
        //比较二者是否相同
        return md5Hex(password + salt).equals(new String(cs1));
    }

    public static String getTimeStamp() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return formatter.format(new Date());
    }

    public static String generateUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    public static String getSaltMD5(String key) {
        // 生成一个16位的随机数
        Random random = new Random();
        StringBuilder sBuilder = new StringBuilder(16);
        sBuilder.append(random.nextInt(99999999)).append(random.nextInt(99999999));
        int len = sBuilder.length();
        if (len < 16) {
            for (int i = 0; i < 16 - len; i++) {
                sBuilder.append("0");
            }
        }
        // 生成最终的加密盐
        String salt = sBuilder.toString();
        return getSaltMD5(key, salt);
    }

    public static String getSaltMD5(String key, String salt) {
        key = md5Hex(key + salt);
        char[] cs = new char[48];
        for (int i = 0; i < 48; i += 3) {
            cs[i] = key.charAt(i / 3 * 2);
            char c = salt.charAt(i / 3);
            cs[i + 1] = c;
            cs[i + 2] = key.charAt(i / 3 * 2 + 1);
        }
        return String.valueOf(cs);
    }

    //把十六进制字符串转换成utf-8格式的字符串
    public static String fromHexString(String hexString) {
        String result;
        hexString = hexString.toUpperCase();
        String hexDigital = "0123456789ABCDEF";
        char[] hexCharArray = hexString.toCharArray();
        byte[] bytes = new byte[hexString.length() / 2];
        int n;
        for (int i = 0; i < bytes.length; i++) {
            n = hexDigital.indexOf(hexCharArray[2 * i]) * 16 + hexDigital.indexOf(hexCharArray[2 * i + 1]);
            bytes[i] = (byte) (n & 0xff);
        }
        result = new String(bytes, StandardCharsets.UTF_8);
        return result;
    }


    // Case 1 utilizing String.regionMatches()
//    @Deprecated
//    public static boolean containsIgnoreCase(String src, String what) {
//        final int length = what.length();
//        if (length == 0)
//            return true; // Empty string is contained
//
//        final char firstLo = Character.toLowerCase(what.charAt(0));
//        final char firstUp = Character.toUpperCase(what.charAt(0));
//
//        for (int i = src.length() - length; i >= 0; i--) {
//            // Quick check before calling the more expensive regionMatches()
//            // method:
//            final char ch = src.charAt(i);
//            if (ch != firstLo && ch != firstUp)
//                continue;
//
//            if (src.regionMatches(true, i, what, 0, length))
//                return true;
//        }
//
//        return false;
//    }

    public static boolean containsIgnoreCase(String str, String toCheck) {
        if (toCheck == null || str == null) {
            return false;
        }
        return str.toUpperCase().contains(toCheck.toUpperCase());
    }

    public static List<List<String>> getStringFields(List<?> entityList) {
        List<List<String>> list = new ArrayList<>();
        for (Object entity : entityList) {
            List<String> stringList = new ArrayList<>();
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    if (field.getType() == String.class) {
                        stringList.add((String) field.get(entity));
                    }
                } catch (IllegalAccessException e) {
                    log.error(e.getMessage(), e);
                }
            }
            list.add(stringList);
        }
        return list;
    }

    public static int hexStrToInt(String hexStr) {
        return Integer.decode(hexStr);
    }

//    @Deprecated()
//    public static int hexStrToDecimalInt(String hexStr) {
//        return Integer.parseInt(hexStr.substring(2), 16);
//    }


    //每两个字符串增加一个空格
    public static String addHexSpace(String input) {
        input = input.trim();
        if (input.matches(".*\\s+.*")) {
            return input;
        }
        StringBuilder sb = new StringBuilder(input);
        int gap = input.length() / 2;
        for (int i = 0; i < gap; i++) {
            sb.insert(2 * i + i, ' ');
        }
        return sb.toString();
    }

    public static boolean compareStrings(String str1, String str2, char x) {
        str1 = str1.toUpperCase().replaceAll("\\s", "");
        str2 = str2.toUpperCase().replaceAll("\\s", "");
        for (int i = 0; i < str1.length(); i++) {
            if (str1.charAt(i) != x && str2.charAt(i) != x && str1.charAt(i) != str2.charAt(i)) {
                return false;
            }
        }
        return true;
    }


    public static int findNumberByString(String string) {
        // 使用正则表达式匹配数字
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(string);
        if (matcher.find()) {
            String number = matcher.group();
            return Integer.parseInt(number);
        }
        return -1;
    }

    public static String splitString(String str, int groupIndex, int groupSize) {
        int startIndex = groupIndex * groupSize; // 计算起始位置
        int endIndex = startIndex + groupSize; // 计算结束位置
        if (str.length() < endIndex) {
            return null;
        }
        return str.substring(startIndex, endIndex);
    }


    public static double removeParenthesesToDouble(String str) {
        return removeParentheses(str, Double.class, 0.0);
    }

    public static int removeParenthesesToInt(String str) {
        return removeParentheses(str, Integer.class, 0);
    }

    /**
     * 通用方法，用于去除字符串中的括号并转换为指定类型
     * @param str 输入字符串
     * @param clazz 目标类型
     * @param defaultValue 默认值
     * @param <T> 泛型参数
     * @return 转换后的值
     */
    private static <T extends Number> T removeParentheses(String str, Class<T> clazz, T defaultValue) {
        if (str == null) {
            log.warn("输入字符串为null");
            return defaultValue;
        }
        if (str.startsWith("(") && str.endsWith(")")) {
            str = str.substring(1, str.length() - 1);
        }
        try {
            if (clazz == Double.class) {
                return clazz.cast(Double.parseDouble(str));
            } else if (clazz == Integer.class) {
                return clazz.cast(Integer.parseInt(str));
            }
        } catch (NumberFormatException e) {
            log.warn("字符串无法转换为" + clazz.getSimpleName(), e);
        }
        return defaultValue;
    }

    public static String hexToAscii(String hexString) {
        // 移除空格
        hexString = hexString.replaceAll("\\s+", "");
        StringBuilder output = new StringBuilder();

        // 每两个字符转换为一个字节 (0-255)
        for (int i = 0; i < hexString.length(); i += 2) {
            String str = hexString.substring(i, i + 2);
            try {
                int decimal = Integer.parseInt(str, 16);
                output.append((char) decimal);
            } catch (NumberFormatException e) {
                System.err.println("Invalid hex character: " + str);
            }
        }
        return output.toString();
    }
    public static boolean isHexadecimal(String message) {
        // 移除所有空格
        String cleanedMessage = message.replaceAll("\\s+", "");
        // 十六进制的正则表达式：必须是偶数长度的字符串，只包含0-9, a-f, A-F的字符
        String hexPattern = "^[0-9A-Fa-f]+$";
        // 检查是否符合正则表达式，并且长度是偶数
        return cleanedMessage.matches(hexPattern) && (cleanedMessage.length() % 2 == 0);
    }


    public static void main(String[] args) {
        int j = StrUtils.hexStrToInt("0x3AA");
        System.out.println(j);
//        String str = "-1";
//        double result = removeParenthesesToDouble(str);
//        System.out.println(Hex.encodeHexString(ByteUtils.hexStringToByteArray("11223344")));
//        System.out.println("转换后的double类型数据为: " + result);

    }


}
