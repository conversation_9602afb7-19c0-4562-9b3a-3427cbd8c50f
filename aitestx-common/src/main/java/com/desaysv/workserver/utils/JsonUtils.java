package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * JSON工具类
 */
@Slf4j
public class JsonUtils {

    public static String convertToValidJson(String input) {
        // 正则表达式匹配键值对，其中键为任意非空白字符，值为非空白字符序列
        // 改进的正则表达式，能够处理布尔值、数字和字符串值
        String regex = "(?<=\\{|,)(\\s*)([\\w]+)\\s*:\\s*([^,\\}]+)(?=,|\\})";
        Pattern pattern = Pattern.compile(regex);
        StringBuffer sb = new StringBuffer();
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String key = matcher.group(2); // 匹配到的键名
            String value = matcher.group(3).trim(); // 匹配到的值
            
            // 添加双引号到键名
            key = "\"" + key + "\"";
            
            // 判断值是否为布尔值或数字，如果不是则添加引号
            if (!value.equals("true") && !value.equals("false") && 
                !value.matches("-?\\d+(\\.\\d+)?")) {
                // 非布尔值和数字需要添加引号
                value = "\"" + value.replaceAll("\\\\", "\\\\\\\\")
                                   .replaceAll("\"", "\\\\\"") + "\"";
            }
            
            // 组合新的键值对
            matcher.appendReplacement(sb, key + ":" + value);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}