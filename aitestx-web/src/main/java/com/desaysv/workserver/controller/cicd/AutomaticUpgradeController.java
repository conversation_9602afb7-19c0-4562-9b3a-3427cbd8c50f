package com.desaysv.workserver.controller.cicd;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.context.ActionSequenceContext;
import com.desaysv.workserver.context.TestStep;
import com.desaysv.workserver.controller.cicd.smokingtest.SmokingTestSseStatus;
import com.desaysv.workserver.entity.ColumnNameConstants;
import com.desaysv.workserver.entity.NotificationManager;
import com.desaysv.workserver.executor.ActionSequenceExecutorService;
import com.desaysv.workserver.entity.SmokingTestConfigModel;
import com.desaysv.workserver.entity.SmokingTestConfigObserver;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.result.ActionSequenceCheckReporter;
import com.desaysv.workserver.result.ActionSequenceCheckService;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static com.desaysv.workserver.utils.JsonUtils.convertToValidJson;

/**
 * 冒烟升级
 */
@Slf4j
@RestController
@RequestMapping("/upgrade")
@Lazy
public class AutomaticUpgradeController {
    private SmokingTestConfigModel smokingTestConfigModel;
    @Autowired
    ActionSequenceExecutorService actionSequenceExecutorService;
    @Autowired
    private ActionSequenceCheckService actionSequenceCheckService;

    private final List<SmokingTestConfigObserver> observers = new ArrayList<>();

    @PostMapping("/notification")
    public ResultEntity<String> upgradeNotification(@RequestBody String modelJson) {
//    public String upgradeNotification(@RequestHeader("Content-Type") String contentType, @RequestBody NotificationModel model) {
        if (smokingTestConfigModel == null) {
            return ResultEntity.fail("没有进行冒烟配置");
        }
        if (!smokingTestConfigModel.isListeningUpgradeNotification()) {
            return ResultEntity.fail("没有选择持续监控升级");
        }
//        modelJson = modelJson.replaceAll("([\\w./-]+):([\\w.:/-]+)", "\"$1\":\"$2\"");
        modelJson = convertToValidJson(modelJson);
        NotificationManager model = JSON.parseObject(modelJson, NotificationManager.class);
        NotificationManager.getInstance().setNotificationManager(model);
        if (model.getBuName().equals("BIC")) {
            SseUtils.pubMsg(SseConstants.UPGRADE_NOTIFICATION_SUBSCRIBE_ID, "notificationUpgrade");
            return ResultEntity.ok("已通知软件升级");
        }
//        定义升级脚本
//        调用执行脚本的方法
        return ResultEntity.fail("不通知软件升级");
    }

    @PostMapping("/start")
    public ResultEntity<String> startUpgrade() {
//        SSEUtils.pubMsg(UPGRADE_NOTIFICATION_SUBSCRIBE_ID, "startUpgrade");
        ActionSequenceContext actionSequenceContext = new ActionSequenceContext();
        ArrayList<TestStep> stepList = new ArrayList<>();
        TestStep testStep = new TestStep();
        testStep.setTestStep("1.CAN-Upgrade-" + smokingTestConfigModel.getFileType().getValue());
        testStep.setType(ColumnNameConstants.getInstance().getOperationStepSequences());
        stepList.add(testStep);
        actionSequenceContext.setPrecondition(stepList);
        actionSequenceContext.setSequenceType(1);
        //上传测试状态~~~~end
        ActionSequenceCheckReporter actionSequenceCheckReporter = actionSequenceCheckService.checkActionSequenceGrammar(actionSequenceContext);
        //全部动作序列检查通过才能执行
        if (actionSequenceCheckReporter.isCheckOk()) {
            actionSequenceExecutorService.executeAsync(actionSequenceContext, actionSequenceCheckReporter);
        }
        if (actionSequenceCheckReporter.isExecuteOk()) {
//            System.out.println("-----completedUpgrade-----------");
            SseUtils.pubMsg(SseConstants.UPGRADE_NOTIFICATION_SUBSCRIBE_ID, "completedUpgrade");
        }
        return ResultEntity.ok(actionSequenceCheckReporter.isExecuteOk() ? "升级成功" : "升级失败");
    }

    @PostMapping("/smokingTestConfig")
    public ResultEntity<String> monitor(@RequestBody SmokingTestConfigModel smokingTestConfigModel) {
        this.smokingTestConfigModel = smokingTestConfigModel;
        notifyObservers();
        return ResultEntity.ok("冒烟配置成功！");
    }

    @PostMapping("/closeUpgradeMonitor")
    public ResultEntity<String> closeUpgradeMonitor() {
        SmokingTestSseStatus.closeAll();
        return ResultEntity.ok("关闭升级监控！");
    }

    //    public static void main(String[] args) throws Exception {
//        String input = "{buName:12.13,buName22:ADS.22,buName33:X:/ADS/XXX/XXX_812.13}";
//        String output1 = input.replaceAll("(\\w+):(\\w+)", "\"$1\":\"$2\"");
//        String output = input.replaceAll("([\\w./-]+):([\\w.:/-]+)", "\"$1\":\"$2\"");
//        System.out.println(output1);
//        System.out.println(output);
//    }
    public void addObserver(SmokingTestConfigObserver observer) {
        observers.add(observer);
    }

    private void notifyObservers() {
        for (SmokingTestConfigObserver observer : observers) {
            observer.update(smokingTestConfigModel);
        }
    }

    public static void main(String[] args) {
//        String input = "{buName:X:\\ADS\\XXX\\12.13}";
        String input = "{buName:12.13,buName22:ADS.22,buName33:X:/ADS/XXX/XXX_812.13}";

        String formattedJson = convertToValidJson(input);
        System.out.println(formattedJson);
    }

}
