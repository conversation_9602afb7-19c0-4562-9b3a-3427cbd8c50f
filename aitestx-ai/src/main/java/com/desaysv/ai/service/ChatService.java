package com.desaysv.ai.service;

import com.desaysv.ai.model.ChatMessage;
import com.desaysv.ai.model.ChatRequest;
import com.desaysv.ai.model.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 聊天服务
 */
@Slf4j
@Service
public class ChatService {
    
    @Autowired
    private ProxyLLMService proxyLLMService;
    
    // 会话存储，实际项目中应该使用数据库或Redis
    private final Map<String, List<ChatMessage>> sessionStore = new ConcurrentHashMap<>();
    
    /**
     * 处理聊天请求
     */
    public ChatResponse chat(ChatRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取或创建会话ID
            String sessionId = request.getSessionId();
            if (sessionId == null || sessionId.isEmpty()) {
                sessionId = generateSessionId();
            }
            
            // 获取会话历史
            List<ChatMessage> sessionHistory = getSessionHistory(sessionId);
            
            // 添加用户消息到历史
            ChatMessage userMessage = ChatMessage.userMessage(request.getMessage());
            sessionHistory.add(userMessage);
            
            // 构建请求参数
            Map<String, Object> parameters = buildParameters(request);
            
            // 发送请求到大模型
            String response = proxyLLMService.sendChatRequest(sessionHistory, parameters);
            
            // 添加助手回复到历史
            ChatMessage assistantMessage = ChatMessage.assistantMessage(response);
            sessionHistory.add(assistantMessage);
            
            // 更新会话存储
            updateSessionHistory(sessionId, sessionHistory);
            
            // 计算耗时
            long duration = System.currentTimeMillis() - startTime;
            
            // 构建响应
            return ChatResponse.builder()
                    .message(response)
                    .sessionId(sessionId)
                    .timestamp(LocalDateTime.now())
                    .success(true)
                    .duration(duration)
                    .build();
                    
        } catch (Exception e) {
            log.error("处理聊天请求时发生错误", e);
            
            long duration = System.currentTimeMillis() - startTime;
            
            return ChatResponse.builder()
                    .message("抱歉，处理您的请求时发生了错误")
                    .sessionId(request.getSessionId())
                    .timestamp(LocalDateTime.now())
                    .success(false)
                    .error(e.getMessage())
                    .duration(duration)
                    .build();
        }
    }
    
    /**
     * 获取会话历史
     */
    public List<ChatMessage> getSessionHistory(String sessionId) {
        return sessionStore.computeIfAbsent(sessionId, k -> new ArrayList<>());
    }
    
    /**
     * 清除会话历史
     */
    public void clearSessionHistory(String sessionId) {
        sessionStore.remove(sessionId);
        log.info("已清除会话历史: {}", sessionId);
    }
    
    /**
     * 获取所有活跃会话
     */
    public Set<String> getActiveSessions() {
        return sessionStore.keySet();
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 构建请求参数
     */
    private Map<String, Object> buildParameters(ChatRequest request) {
        Map<String, Object> parameters = new HashMap<>();
        
        if (request.getParameters() != null) {
            ChatRequest.ModelParameters params = request.getParameters();
            if (params.getTemperature() != null) {
                parameters.put("temperature", params.getTemperature());
            }
            if (params.getMaxTokens() != null) {
                parameters.put("max_tokens", params.getMaxTokens());
            }
            if (params.getTopP() != null) {
                parameters.put("top_p", params.getTopP());
            }
        }
        
        parameters.put("stream", request.isStream());
        
        return parameters;
    }
    
    /**
     * 更新会话历史
     */
    private void updateSessionHistory(String sessionId, List<ChatMessage> history) {
        // 限制历史消息数量，避免内存溢出
        int maxHistorySize = 50; // 最多保留50条消息
        if (history.size() > maxHistorySize) {
            // 保留最近的消息，但保留第一条系统消息（如果有）
            List<ChatMessage> trimmedHistory = new ArrayList<>();
            
            // 查找系统消息
            ChatMessage systemMessage = history.stream()
                    .filter(msg -> "system".equals(msg.getRole()))
                    .findFirst()
                    .orElse(null);
            
            if (systemMessage != null) {
                trimmedHistory.add(systemMessage);
            }
            
            // 添加最近的消息
            int startIndex = Math.max(0, history.size() - maxHistorySize + (systemMessage != null ? 1 : 0));
            for (int i = startIndex; i < history.size(); i++) {
                ChatMessage msg = history.get(i);
                if (!"system".equals(msg.getRole())) {
                    trimmedHistory.add(msg);
                }
            }
            
            sessionStore.put(sessionId, trimmedHistory);
        } else {
            sessionStore.put(sessionId, history);
        }
    }
}
