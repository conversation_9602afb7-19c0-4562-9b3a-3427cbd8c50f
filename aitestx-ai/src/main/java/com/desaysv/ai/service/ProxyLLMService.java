package com.desaysv.ai.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.desaysv.ai.model.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 透传后端大模型服务
 */
@Slf4j
@Service
public class ProxyLLMService {
    
    @Value("${ai.proxy.base-url:http://localhost:23355}")
    private String proxyBaseUrl;
    
    @Value("${ai.llm.api-url}")
    private String llmApiUrl;
    
    @Value("${ai.llm.model:gpt-3.5-turbo}")
    private String defaultModel;
    
    private final RestTemplate restTemplate;
    
    public ProxyLLMService() {
        this.restTemplate = new RestTemplate();
    }
    
    /**
     * 发送聊天请求到透传后端
     */
    public String sendChatRequest(List<ChatMessage> messages, Map<String, Object> parameters) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = buildChatRequestBody(messages, parameters);
            
            // 构建透传请求
            String proxyUrl = proxyBaseUrl + "/proxy?url=" + llmApiUrl;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            log.info("发送聊天请求到透传后端: {}", proxyUrl);
            log.debug("请求体: {}", JSON.toJSONString(requestBody));
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(proxyUrl, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.debug("收到响应: {}", responseBody);
                return extractMessageFromResponse(responseBody);
            } else {
                log.error("请求失败，状态码: {}", response.getStatusCode());
                return "请求失败，状态码: " + response.getStatusCode();
            }
            
        } catch (Exception e) {
            log.error("发送聊天请求时发生错误", e);
            return "发送请求时发生错误: " + e.getMessage();
        }
    }
    
    /**
     * 构建聊天请求体
     */
    private Map<String, Object> buildChatRequestBody(List<ChatMessage> messages, Map<String, Object> parameters) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 设置模型
        requestBody.put("model", parameters.getOrDefault("model", defaultModel));
        
        // 转换消息格式
        requestBody.put("messages", convertMessages(messages));
        
        // 设置参数
        if (parameters.containsKey("temperature")) {
            requestBody.put("temperature", parameters.get("temperature"));
        }
        if (parameters.containsKey("max_tokens")) {
            requestBody.put("max_tokens", parameters.get("max_tokens"));
        }
        if (parameters.containsKey("top_p")) {
            requestBody.put("top_p", parameters.get("top_p"));
        }
        if (parameters.containsKey("stream")) {
            requestBody.put("stream", parameters.get("stream"));
        }
        
        return requestBody;
    }
    
    /**
     * 转换消息格式为OpenAI格式
     */
    private Object convertMessages(List<ChatMessage> messages) {
        return messages.stream()
                .map(msg -> {
                    Map<String, String> message = new HashMap<>();
                    message.put("role", msg.getRole());
                    message.put("content", msg.getContent());
                    return message;
                })
                .toArray();
    }
    
    /**
     * 从响应中提取消息内容
     */
    private String extractMessageFromResponse(String responseBody) {
        try {
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            
            // 尝试解析OpenAI格式的响应
            if (jsonResponse.containsKey("choices")) {
                return jsonResponse.getJSONArray("choices")
                        .getJSONObject(0)
                        .getJSONObject("message")
                        .getString("content");
            }
            
            // 尝试解析其他格式的响应
            if (jsonResponse.containsKey("content")) {
                return jsonResponse.getString("content");
            }
            
            if (jsonResponse.containsKey("response")) {
                return jsonResponse.getString("response");
            }
            
            // 如果无法解析，返回原始响应
            return responseBody;
            
        } catch (Exception e) {
            log.error("解析响应时发生错误", e);
            return responseBody;
        }
    }
}
