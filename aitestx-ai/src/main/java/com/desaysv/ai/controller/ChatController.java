package com.desaysv.ai.controller;

import com.desaysv.ai.model.ChatMessage;
import com.desaysv.ai.model.ChatRequest;
import com.desaysv.ai.model.ChatResponse;
import com.desaysv.ai.service.ChatService;
import com.desaysv.workserver.entity.ResultEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 聊天控制器
 */
@Slf4j
@RestController
@RequestMapping("/ai/chat")
@Tag(name = "AI聊天", description = "AI聊天相关接口")
public class ChatController {
    
    @Autowired
    private ChatService chatService;
    
    /**
     * 发送聊天消息
     */
    @PostMapping("/send")
    @Operation(summary = "发送聊天消息", description = "向AI发送消息并获取回复")
    public ResultEntity<ChatResponse> sendMessage(@RequestBody ChatRequest request) {
        log.info("收到聊天请求: sessionId={}, message={}", request.getSessionId(), request.getMessage());
        
        try {
            ChatResponse response = chatService.chat(request);
            
            if (response.isSuccess()) {
                log.info("聊天响应成功: sessionId={}, duration={}ms", response.getSessionId(), response.getDuration());
                return ResultEntity.ok(response);
            } else {
                log.error("聊天响应失败: sessionId={}, error={}", response.getSessionId(), response.getError());
                return ResultEntity.fail(response.getError(), response);
            }
            
        } catch (Exception e) {
            log.error("处理聊天请求时发生异常", e);
            return ResultEntity.fail("处理聊天请求时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取会话历史
     */
    @GetMapping("/history/{sessionId}")
    @Operation(summary = "获取会话历史", description = "获取指定会话的聊天历史记录")
    public ResultEntity<List<ChatMessage>> getSessionHistory(
            @Parameter(description = "会话ID") @PathVariable String sessionId) {
        
        log.info("获取会话历史: sessionId={}", sessionId);
        
        try {
            List<ChatMessage> history = chatService.getSessionHistory(sessionId);
            return ResultEntity.ok(history);
        } catch (Exception e) {
            log.error("获取会话历史时发生异常", e);
            return ResultEntity.fail("获取会话历史时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 清除会话历史
     */
    @DeleteMapping("/history/{sessionId}")
    @Operation(summary = "清除会话历史", description = "清除指定会话的聊天历史记录")
    public ResultEntity<String> clearSessionHistory(
            @Parameter(description = "会话ID") @PathVariable String sessionId) {
        
        log.info("清除会话历史: sessionId={}", sessionId);
        
        try {
            chatService.clearSessionHistory(sessionId);
            return ResultEntity.ok("会话历史已清除");
        } catch (Exception e) {
            log.error("清除会话历史时发生异常", e);
            return ResultEntity.fail("清除会话历史时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取活跃会话列表
     */
    @GetMapping("/sessions")
    @Operation(summary = "获取活跃会话", description = "获取所有活跃的聊天会话列表")
    public ResultEntity<Set<String>> getActiveSessions() {
        log.info("获取活跃会话列表");
        
        try {
            Set<String> sessions = chatService.getActiveSessions();
            return ResultEntity.ok(sessions);
        } catch (Exception e) {
            log.error("获取活跃会话列表时发生异常", e);
            return ResultEntity.fail("获取活跃会话列表时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 简单聊天接口（兼容性）
     */
    @PostMapping("/simple")
    @Operation(summary = "简单聊天", description = "简化的聊天接口，只需要传入消息内容")
    public ResultEntity<String> simpleChat(
            @Parameter(description = "消息内容") @RequestParam String message,
            @Parameter(description = "会话ID（可选）") @RequestParam(required = false) String sessionId) {
        
        log.info("收到简单聊天请求: sessionId={}, message={}", sessionId, message);
        
        try {
            ChatRequest request = new ChatRequest();
            request.setMessage(message);
            request.setSessionId(sessionId);
            
            ChatResponse response = chatService.chat(request);
            
            if (response.isSuccess()) {
                return ResultEntity.ok(response.getMessage());
            } else {
                return ResultEntity.fail(response.getError());
            }
            
        } catch (Exception e) {
            log.error("处理简单聊天请求时发生异常", e);
            return ResultEntity.fail("处理聊天请求时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查聊天服务是否正常运行")
    public ResultEntity<String> health() {
        return ResultEntity.ok("AI聊天服务运行正常");
    }
}
