package com.desaysv.ai.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 聊天请求模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatRequest {
    
    /**
     * 用户消息
     */
    private String message;
    
    /**
     * 会话ID，用于维持对话上下文
     */
    private String sessionId;
    
    /**
     * 历史消息列表
     */
    private List<ChatMessage> history;
    
    /**
     * 是否流式响应
     */
    private boolean stream = false;
    
    /**
     * 模型参数
     */
    private ModelParameters parameters;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelParameters {
        /**
         * 温度参数，控制生成文本的随机性
         */
        private Double temperature = 0.7;
        
        /**
         * 最大生成token数
         */
        private Integer maxTokens = 2048;
        
        /**
         * top_p参数
         */
        private Double topP = 0.9;
    }
}
