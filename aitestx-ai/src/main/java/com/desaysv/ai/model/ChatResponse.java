package com.desaysv.ai.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 聊天响应模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatResponse {
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果有）
     */
    private String error;
    
    /**
     * 使用的token数量
     */
    private TokenUsage tokenUsage;
    
    /**
     * 响应耗时（毫秒）
     */
    private Long duration;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TokenUsage {
        /**
         * 输入token数
         */
        private Integer promptTokens;
        
        /**
         * 输出token数
         */
        private Integer completionTokens;
        
        /**
         * 总token数
         */
        private Integer totalTokens;
    }
}
