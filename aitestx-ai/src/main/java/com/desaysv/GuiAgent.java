package com.desaysv;

import com.desaysv.ai.service.ChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class GuiAgent implements CommandLineRunner {

    @Autowired
    private ChatService chatService;

    @Override
    public void run(String... args) throws Exception {
        // GUI Agent 启动逻辑
        System.out.println("GuiAgent started successfully!");
    }

    public static void main(String[] args) {
        // 可以在这里添加独立运行的逻辑
    }
}
